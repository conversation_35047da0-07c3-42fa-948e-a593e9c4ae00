﻿// ---------------------------------------------------------------------------
// <copyright file="R9TestLogging.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test
{
    using System.Diagnostics.CodeAnalysis;
    using Microsoft.Extensions.Logging;

    /// <summary>
    /// We only need to define a logging method's signature and the source generator will do the rest.
    /// </summary>
    [ExcludeFromCodeCoverage]
    internal static partial class R9TestLogging
    {
        /// <summary>
        /// The entrance of writing SampleEvent log event
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="record">log record</param>
        [LoggerMessage(Level = LogLevel.Information)]
        public static partial void Log(ILogger logger, [LogProperties(OmitReferenceName = true)] SampleEvent record);

        /// <summary>
        /// The entrance of writing SampleTraceEvent log event
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="record">log record</param>
        [LoggerMessage(Level = LogLevel.Information)]
        public static partial void Log(ILogger logger, [LogProperties(OmitReferenceName = true)] SampleTraceEvent record);

        /// <summary>
        /// The entrance of writing SampleExceptionEvent log event
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="record">log record</param>
        [LoggerMessage(Level = LogLevel.Information)]
        public static partial void Log(ILogger logger, [LogProperties(OmitReferenceName = true)] SampleExceptionEvent record);
    }
}
