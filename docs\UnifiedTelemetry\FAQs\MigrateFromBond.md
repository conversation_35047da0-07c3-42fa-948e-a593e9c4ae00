# Handling events generated by bond files
For events generated by bond files, if you have set ```<BondOptions>--fields</BondOptions>``` for your bond files, the generated fields will be like the following code:
```csharp
[global::Bond.Schema]
[System.CodeDom.Compiler.GeneratedCode("gbc", "********")]
public partial class CustomPIIEvent
    : global::Passive.EventSchema
{
    [global::Bond.Id(10), global::Bond.Type(typeof(global::Bond.Tag.wstring)), global::Bond.Required]
    public string UserEmailField = "";

    [global::Bond.Id(20), global::Bond.Type(typeof(global::Bond.Tag.wstring)), global::Bond.Required]
    public string UserIdField = "";

    [global::Bond.Id(30), global::Bond.Type(typeof(global::Bond.Tag.wstring)), global::Bond.Required]
    public string IPField = "";

    [global::Bond.Id(40), global::Bond.Type(typeof(global::Bond.Tag.wstring)), global::Bond.Required]
    public string MixField = "";
}
```
When use compile-time logging (or say FastLogging), only ```properties``` with ```get()``` method will be dumped as single columns. ```Fields``` without ```get()``` method won't be dumped.

## Solutions

We recommand to create a side-by-side event class and include the original fields.

### For events inherits ```Passive.EventSchema```,
In the above example, the original ```CustomPIIEvent``` can be transfered to:
```csharp
public class CustomPIIEvent : EventSchema
{
    public string UserEmailField { get; set; } = string.Empty;

    public string UserIdField { get; set; } = string.Empty;

    public string IPField { get; set; } = string.Empty;

    public string MixField { get; set; } = string.Empty;

    public SideBySidePIIEvent(string userEmail, string userId, string ipField, string mixField)
    {
        UserEmailField = userEmail;
        UserIdField = userId;
        IPField = ipField;
        MixField = mixField;
    }
}
```
You can create a side-by-side event class like the above sample and inherits [EventSchema.cs](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/R9.Logging.Substrate/EventSchemas/EventSchema.cs).

### For events inherits other schemas from Passive.bond
You can create side-by-side event class which inherits [EventSchemas](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/dev/R9.Logging.Substrate/EventSchemas) provided by ```R9.Logging.Substrate```.

| Original Bond Schema in PassiveMonitoring | Migrated Schemas in R9.Logging.Substrate |
|---------------------|-------------------|
| Passive.EventSchema      | EventSchema  |
| Passive.ScenarioSchema   | ScenarioSchema |
| Passive.TraceSchema      | TraceSchema |
| Passive.ExceptionSchema  | ExceptionSchema  