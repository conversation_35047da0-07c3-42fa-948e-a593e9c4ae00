﻿// <copyright file="SchemaTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test
{
    using System;
    using System.Diagnostics.Tracing;
    using System.IO;
    using System.Text;
    using FluentAssertions;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.Logging;
    using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.EventSchemas;
    using Microsoft.R9.Extensions.Logging.Exporters;
    using Xunit;

    public class SchemaTest
    {
        private const string BasicConfig = @"
{
        ""SubstrateLogging"": { 
        ""R9Logging"": {
            ""MaxStackTraceLength"": 3000
        },
        ""GenevaExporter"": {
            ""ConnectionString"": ""EtwSession=TestSession1"",
            ""TableNameMappings"": {
                ""TraceSchemaLogger"": ""R9TraceEvent"",
                ""EventSchemaLogger"": ""R9SampleEvent"",
                ""ExceptionSchemaLogger"": ""R9ExceptionEvent""
            }
        }
    }
}";

        /// <summary>
        /// Test logger for traceSchema logging.
        /// </summary>
        private ILogger? taceSchemalogger;

        /// <summary>
        /// Test logger for event schema logging.
        /// </summary>
        private ILogger? eventSchemaLogger;

        /// <summary>
        /// Test logger for exception schema logging.
        /// </summary>
        private ILogger? exceptionSchemaLogger;

        /// <summary>
        /// call to initialize normally
        /// </summary>
        internal void InitializeR9SubstrateLogging()
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(BasicConfig)))
                .Build();
            ILoggerFactory loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsoleExporter().ConfigureSubstrateLogging(configuration);
            });

            this.taceSchemalogger = loggerFactory.CreateLogger("TraceSchemaLogger");
            this.eventSchemaLogger = loggerFactory.CreateLogger("EventSchemaLogger");
            this.exceptionSchemaLogger = loggerFactory.CreateLogger("ExceptionSchemaLogger");
        }

        [Fact]
        public void SchemaEventGenerationTest()
        {
            // validate event schema event generation
            SampleEvent sampleEvent = new SampleEvent(0.0, "SampleService");

            var eventfields = sampleEvent.GetType()
           .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            string activityid = sampleEvent.ActivityId;
            string operationid = sampleEvent.OperationId;

            Assert.NotNull(sampleEvent.ActivityId);
            Assert.NotNull(sampleEvent.OperationId);
            Assert.Equal(4, eventfields.Length);
            Assert.Equal(TraceContext.ActivityId.ToString(), activityid);
            Assert.Equal(TraceContext.OperationId.ToString(), operationid);

            // validate trace schema event generation
            SampleTraceEvent sampleTraceEvent = new SampleTraceEvent(0.0, "SampleService");
            SampleTraceEvent sampleTraceEvent2 = new SampleTraceEvent(0.0, "SampleService", "test message", EventLevel.Warning);

            var fields = sampleTraceEvent.GetType()
           .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            var fields2 = sampleTraceEvent2.GetType()
            .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            Assert.NotNull(sampleTraceEvent.MachineProvisioningState);
            Assert.Equal("test message", sampleTraceEvent2.Message);
            Assert.Equal((uint)EventLevel.Warning, sampleTraceEvent2.EventLevel);
            Assert.Equal(14, fields.Length);
            Assert.Equal(14, fields2.Length);

            // validate exception schema event generation
            SampleExceptionEvent exceptionEvent = new SampleExceptionEvent(new Exception("test exception"), "SampleService");
            SampleExceptionEvent exceptionEvent2 = new SampleExceptionEvent(null, "SampleService");

            var exceptionfields = exceptionEvent.GetType()
           .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            Assert.NotNull(exceptionEvent.Message);
            Assert.Equal("System.Exception: test exception", exceptionEvent.Description);
            Assert.Equal((uint)EventLevel.Error, exceptionEvent.EventLevel);
            Assert.Equal(15, exceptionfields.Length);

            Assert.Equal(0U, exceptionEvent2.EventLevel);
            Assert.Equal(string.Empty, exceptionEvent2.Message);
            Assert.Equal(string.Empty, exceptionEvent2.Description);
            Assert.Equal(15, exceptionfields.Length);
        }

        [Fact]
        public void SchemaEventLoggingTest()
        {
            this.InitializeR9SubstrateLogging();
            SampleTraceEvent sampleTraceEvent = new SampleTraceEvent(0.0, "SampleService");
            Assert.NotNull(this.taceSchemalogger);
            var writer = new StringWriter();
            Console.SetOut(writer);
            if (this.taceSchemalogger != null)
            {
                R9TestLogging.Log(this.taceSchemalogger, sampleTraceEvent);
            }
            Assert.NotNull(sampleTraceEvent.MachineProvisioningState);

            // Assert.
            string output = writer.GetStringBuilder().ToString();
            output.Should().Contain("TraceSchemaLogger");
        }
    }
}
