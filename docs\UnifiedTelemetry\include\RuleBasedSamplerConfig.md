**Section Key**: `SubstrateLogging:R9Logging`

**Example**

```json
{
    "SubstrateLogging": {
        "UseCompositeExporter": true,
        "CompositeExporter": {
            "VirtualTableMappings": {
                "Test.MyService": "MyServiceTable"
            },
            "Geneva": {
                "ConnectionString": "EtwSession=test"
            },
            "OdlTcp": {
                "ConnectionString": "tcp://localhost:1234"
            },
            "VirtualTableExports": {
                "MyServiceTable": [
                    {
                        "ExporterType": "Geneva",
                        "ExportTable": "ServiceEvent"
                    },
                    {
                        "ExporterType": "OdlTcp",
                        "ExportTable": "TestLogType"
                    }
                ]
            }
        }
    }
}
```