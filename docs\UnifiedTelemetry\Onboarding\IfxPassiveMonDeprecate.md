The **IFx SDK** is scheduled for deprecation on **September 30, 2026**. For more details, refer to the [IFx SDK Retirement Announcement](https://eng.ms/docs/products/geneva/collect/instrument/ifx/ifx-retirement).

Similarly, the **PassiveMon SDK**, which is built on top of the IFx SDK, will also **be deprecated**. With Microsoft's strategic decision to adopt OpenTelemetry as the company-wide standard for telemetry collection and the migration of most services to the latest .NET, the PassiveMon SDK has become outdated. It no longer aligns with the modern infrastructure required to leverage cutting-edge technologies effectively.

## Nuget Packages to be Retired
- [Microsoft.M365.Core.PassiveMonitoring](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/Enzyme/NuGet/Microsoft.M365.Core.PassiveMonitoring/overview/5.0.16)
- [Microsoft.Office.Datacenter.PassiveMonitoring](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/Enzyme/NuGet/Microsoft.Office.Datacenter.PassiveMonitoring/overview/16.1.5493)

## Retirement Timeline
The retirement will happen in two phases.

### Phase 1 - No new onboarding after January 1st, 2026
New customers must not onboard to the PassiveMon packages. 
Any violation of this will not be supported. 
All feature work and bug fixes will stop. 
Security/compliance issues will still be addressed in a timely manner.


### Phase 2 - PassiveMon SDK end of life on September 30, 2026
PassiveMon SDK will no longer be supported. 
Security/compliance fixes will no longer be addressed.
Customers must complete the migration to R9 Telemetry before this.

## Why Moving to R9 Telemetry
**R9 Telemetry**, built on top of OpenTelemetry, provides robust infrastructure to simplify the adoption of telemetry solutions for Microsoft services while ensuring compliance. As the north star for telemetry collection in Substrate services, R9 Telemetry offers numerous benefits such as Linux-ready, cross-model capable, high-performance, feature-rich, cost-efficient, and legacy-free.

For more details, refer to the [R9 SDK](https://eng.ms/docs/experiences-devices/r9-sdk) and [OpenTelemetry](https://opentelemetry.io/).

## How to R9 Telemetry
1. **Onboarding**: Follow the **R9 Logging/Metering Onboarding guide** to complete the initial setup.
2. **Migration**: Follow this section to mesure the risk and complete the transition.  
	- Step 1: Migrate Instrumentaion
	- Step 2: Check Potential Risks
	- Step 3: Switch Data Seamlessly

## Effort Estimation
It typically takes about **3 developer weeks over a 6-week calendar** period to migrate from the IFx (PassiveMon) SDK to the R9 SDK with data emitting to **MDS**. 
- The migration Pull Request is estimated to take approximately 1.5 developer weeks to complete. 
- Following that, data validation and the switch to R9 will depend on the service team’s schedule, requiring about 1.5 developer week of effort. 
- Due to the ring-by-ring deployment strategy, full saturation across the worldwide ring is expected to take about 6 weeks duration.

The effort would vary and be impacted by 
- Model of the service, Model A/B/B2 or COSMIC 
- The service is executable service or library 
- The Telemetry SDK which is being used now. 
- he dev’s experiences 
- … 

## Sample Pull Requests
- DsApi SDK: https://o365exchange.visualstudio.com/O365%20Core/_git/DsapiSdk/pullrequest/3808523?_a=files
- WLM: https://o365exchange.visualstudio.com/O365%20Core/_git/WLM/pullrequest/4051704?_a=files