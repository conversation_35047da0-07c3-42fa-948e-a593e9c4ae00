﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Logging.Substrate</AssemblyName>
    <EnablePackageValidation>true</EnablePackageValidation>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <TargetFrameworks>net8.0;net6.0;net472</TargetFrameworks>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>Microsoft.M365.Core.Telemetry.R9.Logging.Substrate</PackageId>
    <PackageVersion>1.2.2</PackageVersion>
    <PackageValidationBaselineVersion>1.2.0</PackageValidationBaselineVersion>
    <PackageReleaseNotes>Add EventSchema, ScenarioSchema and TraceSchema to R9.Logging.Substrate. </PackageReleaseNotes>
    <Authors>SOTELS</Authors>
    <Product>Microsoft.M365.Core.Telemetry</Product>
    <Copyright>Copyright 2024 Microsoft Corp. All rights reserved.</Copyright>
    <Description>R9 Logging extension for Substrate use.</Description>
    <PlatformTarget>anycpu</PlatformTarget>
    <LangVersion>10</LangVersion>
    <AssemblyVersion>*******</AssemblyVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="CredSMARTSdk" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Ecs.InternalizedDependencies" />
	  <PackageReference Include="Microsoft.Extensions.Configuration.Ecs.Authentication.InternalizedDependencies" />
	  <PackageReference Include="Microsoft.Identity.Client" />
	  <PackageReference Include="Microsoft.R9.Extensions.Logging" />
	  <PackageReference Include="Microsoft.R9.Extensions.Logging.Exporters.Geneva" />
	  <PackageReference Include="Microsoft.Skype.ECS.Client.InternalizedDependencies" />
	  <PackageReference Include="Microsoft.Skype.ECS.Client.Authentication.InternalizedDependencies" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\R9TelemetryExtensionsCore\Enrichment\Enrichment.csproj" />
    <ProjectReference Include="..\Telemetry.Exporters.ODLTcp\Telemetry.Exporters.ODLTcp.csproj" />
  </ItemGroup>

</Project>
