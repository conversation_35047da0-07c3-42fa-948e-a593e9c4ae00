﻿// <copyright file="ExceptionSchema.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.EventSchemas
{
    using System;
    using System.Diagnostics.Tracing;

    /// <summary>
    /// The exception schema base
    /// </summary>
    public class ExceptionSchema : ScenarioSchema
    {
        /// <summary>
        /// Gets or sets the event level.
        /// </summary>
        public uint EventLevel { get; set; }

        /// <summary>
        /// Gets or sets the exception type.
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Gets or sets the exception message.
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the exception description.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ExceptionSchema"/> class.
        /// </summary>
        /// <param name="ex">The exception</param>
        /// <param name="eventLevel">The level of exception</param>
        public ExceptionSchema(Exception ex, EventLevel eventLevel = System.Diagnostics.Tracing.EventLevel.Error) : base()
        {
            if (ex != null)
            {
                this.EventLevel = (uint)eventLevel;
                this.Type = ex.GetType().Name;
                this.Message = ex.Message;
                this.Description = ex.ToString();
            }
            else
            {
                this.EventLevel = 0;
                this.Type = string.Empty;
                this.Message = string.Empty;
                this.Description = string.Empty;
            }
        }
    }
}
