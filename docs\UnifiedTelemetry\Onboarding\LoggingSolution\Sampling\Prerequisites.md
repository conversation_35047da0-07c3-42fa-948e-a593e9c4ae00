# Step 0 - Prerequisites

Before we start, please make sure the packages are installed correctly with required version:

**Requred Packages**
*   Microsoft.Extensions.Telemetry >= 9.3.0
*   Microsoft.Extensions.Telemetry.Abstractions >= 9.3.0

> [!Warning]
> Breaking change: Microsoft.Extensions.Telemetry does not support net6.0 any more since [9.0.0](https://www.nuget.org/packages/Microsoft.Extensions.Telemetry/9.0.0). Sampling component only support net472 and net8.0 currently.

**Migrate to Dotnet Logging**

Our sampling solution is built on [dotnet extension sampling](https://github.com/dotnet/extensions/blob/main/src/Libraries/Microsoft.Extensions.Telemetry/README.md) interfaces. And it only supports sample logs instrumented by [dotnet logging API](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.ilogger?view=net-9.0-pp).

For customers use **IFx/PassiveMon SDK**, refer to [Migration from IFx/PassiveMon SDK](../../IfxPassiveMonDeprecate.md) first.

For customers already **onboarded R9 or dotnet logging**, we recommend to use our Substrate logging extension to onboard sampling, refer to [R9 Onboarding](../GuideSteps/Prerequisites.md) for more details. Directly using sampling is also supported.

For other dependencies of the above 2 packages (like Microsoft.Extensions.*), please upgrade them accordingly.
Meanwhile, we recommend using the latest `M365.Core.Telemetry.R9.Logging.Substrate` version in [Package Details - Azure Artifacts](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/Enzyme/NuGet/Microsoft.M365.Core.Telemetry.R9.Logging.Substrate/overview) to leverage our latest features.

**Next Step**: [Create Sampling Rules](./CreateSamplingRules.md)