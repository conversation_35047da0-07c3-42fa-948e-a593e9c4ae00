﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Metering.Substrate</AssemblyName>
    <EnablePackageValidation>true</EnablePackageValidation>
    <RootNamespace>$(AssemblyName)</RootNamespace>
    <TargetFrameworks>net8.0;net6.0;net472</TargetFrameworks>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>Microsoft.M365.Core.Telemetry.R9.Metering.Substrate</PackageId>
    <PackageVersion>1.0.6-rc</PackageVersion>
    <PackageValidationBaselineVersion>1.0.3-rc</PackageValidationBaselineVersion>
    <PackageReleaseNotes>>UpdateMicrosoft.Extensions.Telemetry to 8.10.0</PackageReleaseNotes>
    <Authors>SOTELS</Authors>
    <Product>Microsoft.M365.Core.Telemetry</Product>
    <Copyright>Copyright 2024 Microsoft Corp. All rights reserved.</Copyright>
    <Description>R9 Metering extension for Substrate use.</Description>
	<PlatformTarget>anycpu</PlatformTarget>
	<LangVersion>9</LangVersion>
	<AssemblyVersion>*******</AssemblyVersion>
    <Nullable>enable</Nullable>
    <NoWarn>R9EXP0014</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.R9.Extensions.Metering" />
    <PackageReference Include="Microsoft.R9.Extensions.Metering.Exporters.Geneva" />
    <PackageReference Include="Microsoft.Extensions.Telemetry" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\R9TelemetryExtensionsCore\Enrichment\Enrichment.csproj" />
  </ItemGroup>

</Project>
