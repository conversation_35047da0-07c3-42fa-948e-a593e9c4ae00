# Step 6 - Check Potential Risks

## Schema changes

Monitoring Agent will add some fields for Ifx logs only. And R9 logs have equivalent fields with different names for some of them.
IFx SDK follows common schema [v2.1 - Repos](https://msazure.visualstudio.com/One/_git/CommonSchema?path=/v2.1) while R9(Open Telemetry) follows schema [v4.0 - Repos](https://msazure.visualstudio.com/One/_git/CommonSchema?path=/v4.0). So the migrate will introduce a log record schema change. Here I list the details [SchemaChangeDetails.xlsx](https://microsoft.sharepoint.com/:x:/t/szo365fnd/ODIN/Ebmho2QanpNPi6ECtpNSI1kBjAEG004djBdm4SrYhoEqkA?e=2dmJ6a).

![](../../../.images/Logging/UserInstructions/dimentsion-table.png)

We propose to separate data from IFx and R9 by ingested R9 data to new table because
Enable dual ingestion for validation, keep the existing IFx path unaltered until your team approves cleanup.
Known Issue: Geneva dumps to Kusto, where data loss occurs due to a column conflict in a single table. This conflict arises between env_ver (schema 4.0) and Ifx.PartASchema.env_ver (schema 2.1). [Kusto/cosmos streaming error while migrating ifx to opentelemetry.](https://teams.microsoft.com/l/message/19:<EMAIL>/1726015878587?tenantId=72f988bf-86f1-41af-91ab-2d7cd011db47&groupId=5658f840-c680-4882-93be-7cc69578f94e&parentMessageId=1726015878587&teamName=Geneva%20Monitoring%20Discussion&channelName=OpenTelemetry%20.NET%2C%20IFx%20C-sharp&createdTime=1726015878587)

**Regarding data consumption**

**DGrep:**
*   Transition: user need to check both original IFx Table and new R9 Table during transition period, then the result would merge
*   Post-migration: switch to new R9 Table

**Kusto:**
*   Transition: keep previous behavior, OIC Unified Telemetry will update the global function ([Passive Managed Kusto Clusters and Global Function | M365 Passive Monitoring](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-passive-monitoring/m365-passive-monitoring/kusto/kusto-global-function)) to replace the underlying data source.
*   Post-migration: two options either use previous Table or switch to new Table. We recommend switching to the new Table if DGrep is used, otherwise we recommend using the previous table. If switch to the new table, all monitor/alerting rely on this need update accordingly.

## Events generated by bond files
If you found some fields are not logged properly, please check [Events from bond](../../../FAQs/MigrateFromBond.md)

## Summary

**Next Step**: [Switch Data Seamlessly](./MigrationSteps.md)