// <copyright file="ODLTraceExporterExtensionTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Logging.Exporters;
using Microsoft.R9.Extensions.Tracing.Exporters;
using Newtonsoft.Json;
using OpenTelemetry.Trace;
using Xunit;
using Assert = Xunit.Assert;

namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODL.Test
{
    public class ODLTraceExporterExtensionTest
    {
        [Fact]
        public void AddODLExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((TracerProviderBuilder)null!).AddODLExporter((Action<ODLTraceExporterOptions>)null!, true));

            Assert.Throws<ArgumentNullException>(() =>
                    new HostBuilder().ConfigureServices(builder => builder
                            .AddOpenTelemetry().WithTracing(traceProviderBuilder =>
                            {
                                traceProviderBuilder.AddODLExporter((Action<ODLTraceExporterOptions>)null!, true);
                            }))
                        .Build());
            Assert.Throws<ArgumentNullException>(() =>
               {
                   var options = new ODLTraceExporterOptions();
                   options.PrepopulatedFields = new Dictionary<string, string>
                   {
                       ["TestField"] = null,
                   };
               });
        }

        [Fact]
        public void AddODLExporter_GivenValidOptions_RegistersRequiredServices()
        {
            using var host = new HostBuilder()
                        .ConfigureServices(builder => builder
                            .AddOpenTelemetry().WithTracing(traceProviderBuilder =>
                            {
                                traceProviderBuilder.AddODLExporter(
                                    options =>
                                {
                                    options.EnableFallBack = false;
                                    options.PrepopulatedFields = new Dictionary<string, string>
                                    {
                                        ["TestField"] = "TestValue",
                                    };
                                    options.LogTypeMappings = new Dictionary<string, string>
                                    {
                                        ["*"] = "TestEVR",
                                    };
                                }, true);
                            }))
                        .Build();

            var options = host.Services.GetService<IOptions<ODLTraceExporterOptions>>();
            Assert.NotNull(options);
            Assert.IsAssignableFrom<IOptions<ODLTraceExporterOptions>>(options);
        }

        [Fact]
        public void AddODLExporter_UseSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLExporterWithParam");

                var options = new ODLLogExporterOptions();
                jsonConfigRoot.Bind("ODLExporterWithParam", options);
                config.Value = JsonConvert.SerializeObject(options);

                using var host = new HostBuilder()
                            .ConfigureServices(service => service
                                .AddOpenTelemetry().WithTracing(builder => builder
                                .AddODLExporter(config, true)))
                            .Build();
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_UseInvalidSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLExporterEmpty");

                var options = new ODLTraceExporterOptions();
                jsonConfigRoot.Bind("ODLTraceExporterEmpty", options);
                config.Value = JsonConvert.SerializeObject(options);

                //settings not exist
                var configEmpty = jsonConfigRoot.GetSection("ODLExporterNotExist");
                var optionsEmpty = new ODLTraceExporterOptions();
                jsonConfigRoot.Bind("ODLExporterNotExist", optionsEmpty);
                configEmpty.Value = JsonConvert.SerializeObject(optionsEmpty);

                //settings invalid
                var configInvalid = jsonConfigRoot.GetSection("ODLTraceExporterInvalid");
                var optionInvalid = new ODLTraceExporterOptions();
                jsonConfigRoot.Bind("ODLExporterInvalid", optionInvalid);
                configInvalid.Value = JsonConvert.SerializeObject(optionInvalid);

                //whenever empty or invalid or not exist, will use the default value of the options.
                Assert.Equal(configEmpty.Value, config.Value);
                Assert.Equal(configInvalid.Value, config.Value);

                using var host = new HostBuilder()
                            .ConfigureServices(service => service
                                .AddOpenTelemetry().WithTracing(builder => builder
                                .AddODLExporter(config, true)))
                            .Build();
            });
            Assert.Null(exception);
        }

        [Fact]
        public void AddODLExporter_UseInvalidFile_ThrowException()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings2.json").Build();
            });
            Assert.NotNull(exception);
        }
    }
}