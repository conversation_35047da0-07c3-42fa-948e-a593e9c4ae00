We provide methods to load both local appsettings.json file and ECS.
For the same key, value from ECS will override it in the local file.

# [DI](#tab/DI)

If you are using `IHostBuilder`, append [`IHostBuilder.RegisterConfiguration()`](../SDKs/LoggingExtensions/Reference/APIRef.md) to add an extra ECS source.

**Note**: Please ensure the configuration from `appsettings.json` has been loaded before calling this API.

```csharp
var host = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration((context, config) =>
    {
        config.SetBasePath(Directory.GetCurrentDirectory());
        config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
    })
    .RegisterConfiguration()
```

# [Non-DI](#tab/nonDI)

If you are not using `IHostBuilder`, use [`ConfigurationHelper.LoadConfiguration()`](../SDKs/LoggingExtensions/Reference/APIRef.md) to get the `IConfiguration`.
The result `IConfiguration` combines setting from local file (specified by parameter) and ECS.

**Note**: Please ensure the configuration from `appsettings.json` has been loaded before calling this API.

```csharp
    IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
        Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json")
    );
```