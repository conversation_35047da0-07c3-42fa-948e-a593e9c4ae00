## 1. Branches
We use centralized version control. In Directory.Packages.props we define a parameter (R9version) to control the version of packages like Microsoft.R9.Extensions-*.<br>
Currently Coral repo supports three R9 versions: 1.18, 1.28 and 1.35. <br>
When target is 1.18, we should base on the branch 'release/TelemetryCore_1.0.18'.<br>
When target is 1.28, we should base on the branch 'release/TelemetryCore_1.0.53'.<br>
When target is 1.35, we should base on the branch 'release/TelemetryCore_1.0.73'.<br>
When target is latest, we should base on the branch 'master'.

Additionally, 'release/TelemetryCore_1.0.130' is designated for ESS, which currently utilizes ECS version 20.X.X. There are no immediate plans to upgrade this in Substrate. The merge to master will occur once the corresponding version is upgraded in Substrate.

## 2. Code change
First, run /init.cmd for initailization.<br>
After that, we can open /sources/dev/TelemetryCore.sln to make code change. It's a virtual solution where we can add dev code in the dev subfolder and add unit test in the test subfolder.
We should always pass all unit tests for all the projects.<br>
When we want to add code and publish new packages to common feed, we need to make change in /build/configuration/public/xml/PackageInfo.xml.
Specially, if base branch is not master, we still need to do the same change in /build/configuration/public/xml/PackageInfo.xml via a new PR based on master branch.

## 3.2 NuGet version control
Official version will be [Major].[Minor].[Patch], and prerelease build will generate [Major Version].[Minor Version].[Patch-Suffix[Number]] versions.

Major version 6 will support R9 version 1.18.0, targeting Substrate repo, and major version 7 will support R9 version 1.28.2, targeting other coral repos. Major versions shall only be updated if we decide to support higher versions of R9 libraries, or we need to deal with really crucial breaking changes.

Minor versions will be updated in each official release. If same features need to be apply to both Substrate and Coral repo, their minor versions shall be the same. If the update is not for both repo, the minor version shall increment for the targeting repo, but the version targeting the other repo shall be preserved. (For instance, if we have 6.13.0 and 7.13.0 right now, but a feature update only targeting substrate need to to published officially, 6.13.0 will go to 6.14.0, but the next official release will be 6.15.0 and 7.15.0).

Patch will be done on the release branches. Depending on the situation, we might also need to cherry pick it to the master branch.

R9-1.18-based packages should use <b>6</b> as major version. 

R9-1.28-based packages should use <b>7</b> as major version. 

R9-1.35-based packages should use <b>8</b> as major version. 

R9-latest-based packages should use <b>9</b> as major version. 


## 3.4 NuGet release process
After code checkin, run [pipelines](https://o365exchange.visualstudio.com/O365%20Core/_build?definitionId=32944), select the branch 'master' or 'release/TelemetryCore_1.0.18' or 'release/TelemetryCore_1.0.53' based on your target.
Select other branches will always end in failure.
After the pipeline succeeds, click 'Extension' tab -> 'Drop (Browse)',  it will open a new web page.
In the new page, navigate to /target/distrib, we can see all the packages are drop there. Check if their versions are what we want.

Then in the pipeline page, click 'Release' tab -> 'TelemetryCore Prerelease NuGet Upload', run the publish pipeline. One approval is needed(actually you can approve it by yourself).<br>
Finally, search the pacakges in [Artifacts](https://o365exchange.visualstudio.com/O365%20Core/_artifacts/feed/Enzyme).
We should find coressponding version in the 'Versions' tab or 'Upstream Versions' tab.
