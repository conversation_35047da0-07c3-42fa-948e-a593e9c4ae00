<?xml version="1.0" encoding="utf-8"?>
<!--
  This root MSBuild file is automatically imported for all projects in the tree by MSBuild 15.0.
  You can have a hierarchy of imports but make sure that this file is still imported.
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Label="Global locations">
    <!--
      $(MSBuildAllProjects) is a list of files that determine if a project is up-to-date or not.  By including this
      file in the list, it ensures that all projects will be rebuilt if it changes.
    -->

    <!--
      Enlistment root is based off of wherever this file is.  Be sure not to set this property anywhere else.
    -->
    <EnlistmentRoot>$(MSBuildThisFileDirectory.TrimEnd('\\'))</EnlistmentRoot>
    <ProjectDirRelativeToBaseDir>$(MSBuildProjectDirectory.Substring($(EnlistmentRoot.Length)))</ProjectDirRelativeToBaseDir>

  </PropertyGroup>

  <PropertyGroup Label="Default Configurations">
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
  </PropertyGroup>

  <PropertyGroup Label="Target Output Paths">
    <Obj Condition="'$(Configuration)' == 'Debug'">objd</Obj>
    <Obj Condition="'$(Configuration)' != 'Debug'">obj</Obj>
    <TargetRoot>$(EnlistmentRoot)\target</TargetRoot>
    <DistribRoot>$(TargetRoot)\distrib</DistribRoot>
    <PackageOutputPath>$(DistribRoot)\$(MSBuildProjectName)</PackageOutputPath>
    <PackageLocation>$(PackageOutputPath)</PackageLocation>
    <!-- The location that PackageInfo.xml looks for -->
    <PackageInfoXmlRootPath>$(EnlistmentRoot)\target\distrib\InternalPackages\all\$(Configuration)\$(Platform)</PackageInfoXmlRootPath>
    <BaseOutputPath>$(TargetRoot)\$(ProjectDirRelativeToBaseDir)\</BaseOutputPath>
    <OutputPath Condition=" '$(BuildingInsideVisualStudio)' != 'true' ">$(DistribRoot)\$(Configuration)\$(Platform)\$(MSBuildProjectName)\</OutputPath>
  </PropertyGroup>

  <!-- Assign the signing type, cloudsign or prs -->
  <Import Project="$(EnlistmentRoot)\build\Extensions\Signing\Signing.props" Condition="Exists('$(EnlistmentRoot)\build\Extensions\Signing\Signing.props')" />

  <!-- Warnings as errors -->
  <PropertyGroup>
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    <!--Add this property to avoid error, see https://eng.ms/docs/experiences-devices/r9-sdk/v8/net8.-->
    <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>

  <PropertyGroup>
    <RestoreNuGetExePath>$(EnlistmentRoot)\build\nuget\nuget.exe</RestoreNuGetExePath>
    <!-- Do not add the following globs to default items -->
    <DefaultItemExcludes>QTestLogs\**;Logs\**</DefaultItemExcludes>
    <!--
      The full path to an MSBuild .props file that contains the version information generated during QuickBuild VersionGeneration.
    -->
    <QVersionPropertyFile Condition="'$(QVersionPropertyFile)' == ''">$(EnlistmentRoot)\data\Version.props</QVersionPropertyFile>
  </PropertyGroup>
  
    <!--
    Import the QVersionPropertyFile if it exists and this is not the qversiongen project itself
  -->
  <Import Project="$(QVersionPropertyFile)" Condition=" '$(MSBuildProjectFile)' != 'versiongen.proj' And Exists($(QVersionPropertyFile)) " />

  <Import Project="$(EnlistmentRoot)\build\configuration\private\props\RoslynAnalysis.props" />

  <PropertyGroup Condition=" '$(MSBuildProjectExtension)' == '.nuproj'" >
    <NuProjPath Condition=" '$(NuProjPath)' == '' ">$(EnlistmentRoot)\packages\NuProj.0.11.30\tools</NuProjPath>
  </PropertyGroup>
  <Import Project="$(EnlistmentRoot)\build\Extensions\build.props"/>

  <!-- auto import nuproj common into csproj projects so nuproj projects can collect outputs. -->
  <ItemGroup Condition="!Exists('$(MSBuildProjectDirectory)\packages.config')" >
    <PackageReference Include="NuProj.Common" PrivateAssets="All" />
  </ItemGroup>
 

  <!-- Produce deterministic assemblies, requirement for ProduceReferenceAssembly -->
  <PropertyGroup>
    <Deterministic>true</Deterministic>
  </PropertyGroup>

  <!-- Speed up incremental builds in VS and support interface-aware caching in CloudBuild -->
  <PropertyGroup>
    <ProduceReferenceAssembly Condition="'$(MSBuildProjectExtension)' == '.csproj' And '$(UsingMicrosoftNoTargetsSdk)' != 'true'">true</ProduceReferenceAssembly>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Enable packages.config restore -->
    <RestorePackagesConfig Condition=" '$(RestorePackagesConfig)' == '' ">true</RestorePackagesConfig>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Enable optimized NuGet restore -->
    <RestoreUseStaticGraphEvaluation>true</RestoreUseStaticGraphEvaluation>
  </PropertyGroup>

  <PropertyGroup>
    <!-- The unified package version for R9 Telemetry Extensions Core to release -->
    <!-- For versioning convention, please refer to file /docs/DesignOverview.md (branch master) -->
    <R9TelemetryExtensionsCorePackageVersion>10.0.6</R9TelemetryExtensionsCorePackageVersion>
    <R9TelemetryExtensionsCoreReleaseNotes>Update R9 packages to Version 8.11.2.</R9TelemetryExtensionsCoreReleaseNotes>
  </PropertyGroup>

</Project>
