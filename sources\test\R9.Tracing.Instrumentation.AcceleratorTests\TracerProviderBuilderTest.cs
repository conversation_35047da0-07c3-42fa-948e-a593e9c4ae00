﻿// <copyright file="TracerProviderBuilderTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.R9.Extensions.Tracing.Exporters;
using OpenTelemetry;
using OpenTelemetry.Trace;
using Xunit;

namespace Microsoft.M365.Core.Telemetry.R9.Tracing.Instrumentation.Accelerator.Tests
{
    /// <summary>
    /// TracerProviderBuilderTest
    /// </summary>
    public class TracerProviderBuilderTest
    {
        /// <summary>
        /// AddConsoleExporterDisabled
        /// </summary>
        [Fact]
        public void AddConsoleExporterDisabled()
        {
            var services = new ServiceCollection();

            services.AddOpenTelemetry().WithTracing(builder => builder
                .AddConsoleExporter(new ConsoleTracingExporterOptionsInherited()));

            Assert.Throws<InvalidOperationException>(() => services.BuildServiceProvider().GetRequiredService<BaseProcessor<Activity>>());
        }

        /// <summary>
        /// AddHttpTracingDisabled
        /// </summary>
        [Fact]
        public void AddHttpTracingDisabled()
        {
            var services = new ServiceCollection();
            services.AddOpenTelemetry().WithTracing(builder => builder
                    .AddHttpTracing(
                        new HttpTracingOptionsInherited
                    {
                        IsEnabled = false,
                    }));
            Assert.Throws<InvalidOperationException>(() => services.BuildServiceProvider().GetRequiredService<HttpTraceEUIIRedactEnricher>());
        }

        /// <summary>
        /// AddHttpClientTracingDisabled
        /// </summary>
        [Fact]
        public void AddHttpClientTracingDisabled()
        {
            var services = new ServiceCollection();
            services.AddOpenTelemetry().WithTracing(builder => builder
                   .AddHttpClientTracing(
                       new HttpClientTracingOptionsInherited
                       {
                           IsEnabled = false,
                       }));

            Assert.Throws<InvalidOperationException>(() => services.BuildServiceProvider().GetRequiredService<HttpClientEUIIRedactEnricher>());
        }

        /// <summary>
        /// TestGenevaExporterExtensionWithoutSampler
        /// </summary>
        [Fact]
        public void AddGenevaExporterWithoutSamplerTest()
        {
            var services = new ServiceCollection();

            services.AddOpenTelemetry().WithTracing(builder =>
            {
                builder.AddGenevaExporter(new GenevaTraceExporterOptionsInherited());
            });

            Assert.NotNull(services.BuildServiceProvider().GetRequiredService<IOptionsMonitor<GenevaTraceExporterOptions>>());
        }

        /// <summary>
        /// TestODLExporterExtensionWithoutSampler
        /// </summary>
        [Fact]
        public void AddODLExporterWithSamplerTest()
        {
            var services = new ServiceCollection();

            services.AddOpenTelemetry().WithTracing(builder =>
            {
                builder.AddODLExporter(new ODLTraceExporterOptionsInherited(), new AlwaysOnSampler());
            });

            Assert.NotNull(services.BuildServiceProvider().GetRequiredService<IOptionsMonitor<ODLTraceExporterOptions>>());
        }

        /// <summary>
        /// TestODLExporterExtensionWithSampler
        /// </summary>
        [Fact]
        public void AddODLTcpExporterWithoutSamplerTest()
        {
            var services = new ServiceCollection();

            services.AddOpenTelemetry().WithTracing(builder =>
            {
                builder.AddODLTcpExporter(new ODLTcpTraceExporterOptionsInherited(), new AlwaysOnSampler());
            });

            Assert.NotNull(services.BuildServiceProvider().GetRequiredService<IOptionsMonitor<ODLTraceExporterOptions>>());
        }

        /// <summary>
        /// Test odl exporter DI can parse the options.
        /// </summary>
        [Fact]
        public void AddODLExporterOptions()
        {
            var services = new ServiceCollection();
            var options = new ODLTraceExporterOptionsInherited
            {
                EnableFallBack = true,
                LogTypeMappings = new System.Collections.Generic.Dictionary<string, string>
                {
                    { "test", "test" },
                },
            };

            services.AddOpenTelemetry().WithTracing(builder =>
            {
                builder.AddODLExporter(options);
            });

            Assert.NotNull(services.BuildServiceProvider().GetRequiredService<IOptionsMonitor<ODLTraceExporterOptions>>());
        }

        /// <summary>
        /// Test odl tcp exporter DI can parse the options.
        /// </summary>
        [Fact]
        public void AddODLTcpExporterOptions()
        {
            var services = new ServiceCollection();
            var options = new ODLTcpTraceExporterOptionsInherited
            {
                RecordCountPerRequest = 1,
                EnableCleanUpArchivedFiles = true,
                LogTypeMappings = new System.Collections.Generic.Dictionary<string, string>
                {
                    { "test", "test" },
                },
            };

            services.AddOpenTelemetry().WithTracing(builder =>
            {
                builder.AddODLTcpExporter(options);
            });

            Assert.NotNull(services.BuildServiceProvider().GetRequiredService<IOptionsMonitor<ODLTraceExporterOptions>>());
        }
    }
}
