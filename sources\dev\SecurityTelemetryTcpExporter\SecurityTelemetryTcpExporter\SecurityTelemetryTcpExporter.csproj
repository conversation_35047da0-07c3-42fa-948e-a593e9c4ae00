﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net472;net6.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <LangVersion>10.0</LangVersion>
    <AssemblyName>Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter</RootNamespace>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageId>Microsoft.M365.Core.Telemetry.ODL.SecurityTelemetryTcpExporter</PackageId>
    <PackageVersion>8.0.16</PackageVersion>
    <PackageReleaseNotes>Add message count on sdksource level</PackageReleaseNotes>
    <Authors>SOTELS</Authors>
    <Product>Microsoft.M365.Core.Telemetry</Product>
    <Copyright>Copyright 2021 Microsoft Corp. All rights reserved.</Copyright>
    <Description>ODL Nrt TCP exporter for Security Telemetry.</Description>
  </PropertyGroup>

  <ItemGroup>
    <None Include="..\.editorconfig" Link=".editorconfig" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.R9.Extensions.SecurityTelemetry" />
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ODLNrtTcpClient\Microsoft.M365.ODL.NrtTcpClient\Microsoft.M365.ODL.NrtTcpClient.csproj" />
  </ItemGroup>

</Project>
