**Section Key**: `ECSParameters`

Totally there are 5 sections related to ECS.
- `ECSIdentifiers`(**required**): In order to optimize performance and get the configuration precisely, it's required that customers pass the `ServiceName` identifier in this section. Other identifers are optional and should align with ECS portal's filter settings.
- `EnvironmentType` indicates the corresponding ECS endpoint to use. `Production` for prod endpoint, and `Integration` for integration endpoint. If other values are provided, the validation logic will throw `ArgumentException`.
- `Client` is the name of ECS client. It corresponds to the value on left-upper corner of [ECS Portal](https://ecs.skype.com/UnifiedTelemetry/Log/configurations?type=Default&view=shared). 
- `Agents` includes the project teams under the client, also known as `Project Teams`. This field should be an **array** even if only 1 project team is included, otherwise exception will be thrown during initialization.
- `AuthenticationOption`: is required only when you want to use your own ECS client and agents. In most cases, you can use our Production ECS for centralized configuration management, and our Integration ECS for quick validations.

**Example**

1. The simplest case is only pass the `ECSIdentifiers:ServiceName`. In this case, you'll automatically connect to our [Production ECS](https://ecs.skype.com/UnifiedTelemetry/Log/configurations?type=Default&view=shared)
```json
{
    "ECSParameters": {
        "ECSIdentifiers": {
            "ServiceName": "<Your service name>"
        }
    }
}
```
You could also connect to our Production ECS with specified parameters.
```json
{
    "ECSParameters": {
        "ECSIdentifiers": {
            "ServiceName": "<Your service name>"
        },
        "EnvironmentType": "Production",
        "Client": "UnifiedTelemetry",
        "Agents": [ "Log" ]
    }
}
```
2. We support validations during onboarding via our [Integration ECS](https://ecs.skype.net/UnifiedTelemetry/Logs/configurations?type=Default&view=shared). We also provided [sample apps](https://o365exchange.visualstudio.com/O365%20Core/_git/TelemetryCore?path=/sources/sample/R9.Logging.Substrate&version=GBmaster&_a=contents) for quick testing and validation.
```json
{
    "ECSParameters": {
        "ECSIdentifiers": {
            "ServiceName": "<Your service name>"
        },
        "EnvironmentType": "Integration",
        "Client": "UnifiedTelemetry",
        "Agents": [ "Log" ]
    }
}
```

3. If you want to use your own ECS client and agents, please follow the example **strictly**. You should be responsible for your settings' feasibility as we don't have any validation for customized ECS settings. You may refer to [example](https://skype.visualstudio.com/SCC/_git/client-shared_configuration_ecsdotnetclient?path=/samples/AspNetCoreWebApp/appsettings.json&version=GBmaster&line=3&lineEnd=5&lineStartColumn=5&lineEndColumn=60&lineStyle=plain&_a=contents) and [document](https://eng.ms/docs/experiences-devices/m365-core/substrate-platform/substrate-change-management/m365-experimentation-and-configuration-ecs/ecs-user-manual/library/dotnetsdk/features/authentication/aadauth_v20_2) to fill the `AuthenticationOption` section.
```json
{
    "ECSParameters": {
        "ECSIdentifiers": {
            "ServiceName": "<Your service name>"
        },
        "EnvironmentType": "<Your environment>",
        "Client": "<Your client>",
        "Agents": [ "<Your agents>" ],
        "AuthenticationOption": { 
            <Your authentication settings>
        }
    }
}

```

> [!Important]
> - Please double check the values of parameters, because we don't explicitly throw exception to support customization. The logging behavior will use default values and can't be controlled by ECS if it fails to connect to ECS server.
> - If you want to use our ECS client and project team, and want to view or edit the ECS configurations by yourself, please apply for SG `SUBSTRATE_R9_LOGGING_USERS` at https://aks.ms/idweb

After populating the ECS parameter sections with your expected values, you are ready to put the configurations onto [ESC Portal](https://ecs.skype.com/UnifiedTelemetry/Log/configurations?view=shared). It's **suggested** that only put the configurations which have dynamic values and need hot reloading onto ECS.

In following example, the filter `ServiceName` should be same as defined in `ECSParameters:ECSIdentifiers:ServiceName`. 
![alt text](../../../.images/Logging/StepByStep/ecs-example.png)

### Fallback
In case disconnectivity with ECS, it's **highly suggested** you set the stable fallback behavior in `appsettings.json`. Otherwise the ECS-managed configurations would fall back to default values, which may not meet your expectations.

> [!Note]
> Once the ECS configuration is fetched, it will **override** the entries with same configuration keys in `appsettings.json`.