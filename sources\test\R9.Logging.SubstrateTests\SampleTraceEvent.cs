﻿// <copyright file="SampleTraceEvent.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test
{
    using System.Diagnostics.Tracing;
    using Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.EventSchemas;

    public class SampleTraceEvent : TraceSchema
    {
        public double Latency { get; set; }

        public string ServiceName { get; set; }

        public SampleTraceEvent(double latency, string serviceName, string message, EventLevel eventLevel) : base(message, eventLevel)
        {
            Latency = latency;
            ServiceName = serviceName;
        }

        public SampleTraceEvent(double latency, string serviceName) : base()
        {
            Latency = latency;
            ServiceName = serviceName;
        }
    }
}
