# Step 5 - Check Potential Risks

## Cardinality limit

The number of unique combinations of attributes is called cardinality. OpenTelemetry SDK [aggregates](https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/sdk.md#aggregation) metrics using certain algorithms and memory management strategies to achieve good performance and efficiency. The aggregation logic respects [cardinality limits](https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/sdk.md#cardinality-limits), so the SDK does not use an indefinite amount of memory when there is cardinality explosion. Cardinality limit is a throttling mechanism which allows the metrics collection system to have a predictable and reliable behavior when excessive cardinality happens.

OpenTelemetry has a default cardinality limit of 2000 per metric. If the total metric points for a metric stream go beyond 2000 then the metric points will be silently dropped. This limit can be configured by "MaxMetricPointsPerStream" in the above [Configuration](Configuration.md). While we cannot increase the value too largely because it will consume a large amount of memory. Please firstly estimate your metrics’ cardinality, then set proper values.

### Estimate current metric cardinality

Utilize [Estimate impact of new metrics/preaggregates](https://eng.ms/docs/products/geneva/metrics/howdoi/preaggregateestimation) to evaluate the dimension combination (which contribute to in-process cardinality). For a single metric, add a pre-aggregated query with dimensions:
- Exclude dimensions that don’t change in a single process: MachineName, DeployRing, Forest…
- For the remaining dimensions:
  - (minor case) If the combination has been used as pre-aggregation, check the insight table to check its cardinality.
  - Otherwise, add the combination as a new pre-aggregation, and check “Estimation Only” to produce insight data.

Enabling “Estimation Only” mode will cause the Metrics Extension to publish diagnostic data used to calculate the cost of time series and events. While enabled no time series or events will be published to Geneva Metrics. After saving the query for a while, we will see a new dashboard named “Metric Cardinality Estimations” under MonMon folder in your MDM account.

Example:

Below dashboard shows the results of cardinality estimations of two DsApi SDK metrics in the past 7 days. “Time Series” is the metric cardinality values. We can see the cardinality is 1184 for AdkAvailabilityMetric over the data of the past week. Considering business growth, we suggest setting a slightly larger value in the configuration.

![cardinality](../../../.images/Metering/UserInstructions/cardinality.png)

### Delta Aggregation with metric point reclaim

Before OpenTelemetry 1.10, SDK won’t reclaim unused metric data points. It means that right after a service starts up, all historical metric points are kept in memory regardless of whether they are used in current/future or not. After Open Telemetry is upgraded to 1.10.0, under the usage of [Delta Aggregation Temporality](https://github.com/open-telemetry/opentelemetry-specification/blob/main/specification/metrics/data-model.md#temporality), it is possible to choose a smaller cardinality limit because the SDK will reclaim unused metric points automatically. The cardinality limit won’t be an issue.

- Cumulative temporality means that successive data points repeat the starting timestamp. For example, from start time T0, cumulative data points cover time ranges (T0, T1], (T0, T2], (T0, T3], and so on.

- Delta temporality means that successive data points advance the starting timestamp. For example, from start time T0, delta data points cover time ranges (T0, T1], (T1, T2], (T2, T3], and so on.

We are currently working on upgrading OpenTelemetry package in Substrate.

  
**Next Step (for migration only)**: [Switch Data Seamlessly](./MigrationSteps.md)