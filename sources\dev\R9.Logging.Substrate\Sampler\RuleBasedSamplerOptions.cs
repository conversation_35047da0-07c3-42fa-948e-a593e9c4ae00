// <copyright file="RuleBasedSamplerOptions.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Represents the options for rule-based sampling.
    /// </summary>
    internal class RuleBasedSamplerOptions
    {
        /// <summary>
        /// The top level configuration key for the rule-based sampler.
        /// </summary>
        [ConfigurationKeyName("RuleBasedSampler")]
        public Dictionary<string, List<SamplingRule>> Sampler { get; set; } = new ();
    }

    /// <summary>
    /// Represents a sampling rule.
    /// </summary>
    internal class SamplingRule
    {
        /// <summary>
        /// The constraint section for the sampling rule.
        /// </summary>
        public List<Constraint> Constraints { get; set; } = new ();

        /// <summary>
        /// The strategy parameter section for the sampling rule.
        /// </summary>
        [ConfigurationKeyName("Strategy")]
        public StrategyParameter StrategyParameter { get; set; } = new ();
    }

    /// <summary>
    /// Represents a constraint for a sampling rule.
    /// </summary>
    internal class Constraint
    {
        private string ruleOperator = string.Empty;
        private string type = string.Empty;

        /// <summary>
        /// Field name.
        /// </summary>
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// Field value.
        /// </summary>
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// Operator to apply to field.
        /// </summary>
        [ConfigurationKeyName("Operator")]
        public string RuleOperator 
        { 
            get => ruleOperator;
            set => ruleOperator = value?.Trim() ?? string.Empty;
        }

        /// <summary>
        /// Field type to give a precise description of runtime type.
        /// </summary>
        public string Type 
        { 
            get => type;
            set => type = value?.Trim() ?? string.Empty;
        }
    }

    /// <summary>
    /// Represents the strategy parameter for a sampling rule.
    /// </summary>
    internal class StrategyParameter
    {
        private string type = string.Empty;

        /// <summary>
        /// Name of specific strategy to use.
        /// </summary>
        public string Type 
        { 
            get => type;
            set => type = value?.Trim() ?? string.Empty;
        }

        /// <summary>
        /// Expected sample rate between [0,1].
        /// For example, 0.3 means 30% of logs are expected to be sampled.
        /// If sampled, keep the log. If not, discard the log.
        /// </summary>
        public double SampleRate { get; set; }

        /// <summary>
        /// Hash key used in hash-based random sampling.
        /// Should be null for other strategies.
        /// </summary>
        public string? HashKey { get; set; }
    }
}
