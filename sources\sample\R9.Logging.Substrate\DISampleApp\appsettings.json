{"SubstrateLogging": {"UseCompositeExporter": true, "CompositeExporter": {"VirtualTableMappings": {"TableA": "TableA", "TableB": "TableB", "DISampleConsoleApp.SampleService": "TableA", "*": "MyTable"}, "Geneva": {"ConnectionString": "EtwSession=TestSession"}, "OdlTcp": {"ConnectionString": "tcp://localhost:1234"}}}, "ECSParameters": {"ECSIdentifiers": {"ServiceName": "ServiceA"}, "EnvironmentType": "Integration", "Client": "UnifiedTelemetry", "Agents": ["Log"]}}