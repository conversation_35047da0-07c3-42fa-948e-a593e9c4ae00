# [DI](#tab/DI)
For DI projects, the configuration is loaded in Host. And R9 will be initialized in IServiceCollection.

Use method [`IServiceCollection.AddSubstrateLogging()`](../SDKs/LoggingExtensions/Reference/APIRef.md#addsubstrateloggingiservicecollection-iconfiguration-action) to initialize.

```csharp
    /// Load configuration in host
    var host = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration((context, config) =>
    {
        /// option 1: load disk file
        config.SetBasePath(Directory.GetCurrentDirectory());
        config.AddJsonFile("appsettings.json", optional:false, reloadOnChange:true);
        /// option 2: load from JSON stream
        config.AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(configDataInJSON.ToString())));
    })
    .ConfigureServices((context, services) =>
    {
        /// This is the R9 initialization API provided by Unified Telemetry team in Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.
        services.AddSubstrateLogging(context.Configuration);
        services.AddSingleton<IHostedService, SampleService>();
    })
    .Build();
```

# [Non-DI](#tab/nonDI)
For Non-DI projects, we’ll create a static LoggerFacotry and load configuration manually to configure it.

Use [`ILoggerFactory.ConfigureSubstrateLogging`](../SDKs/LoggingExtensions//Reference/APIRef.md#configuresubstrateloggingiloggingbuilder-iconfiguration).

[!code-csharp[](./NonDIInitLogger.cs)]