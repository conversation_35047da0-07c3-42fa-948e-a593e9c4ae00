# Step 0 - Prerequisites

Before we start, please make sure the packages are installed and all package versions should be aligned with Substrate Repo. [Packages.props - Repos](https://o365exchange.visualstudio.com/O365%20Core/_git/Substrate?path=%2FPackages.props)

**Required Packages**
- Microsoft.R9.Extensions.*
- Microsoft.Extensions.*
- OpenTelemetry*
- Microsoft.M365.Core.Telemetry.R9.Metering.Substrate.


We recommend using the latest `M365.Core.Telemetry.R9.Metering.Substrate` version to leverage our latest features. You can see package info from [release note](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-unified-telemetry-logsmetrics/m365-unified-telemetry/sdks/meteringextensions/releasenote)

**Next Step**: [Set up Configuration](./Configuration.md)