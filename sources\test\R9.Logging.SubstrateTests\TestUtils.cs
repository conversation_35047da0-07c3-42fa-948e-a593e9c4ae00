﻿// <copyright file="TestUtils.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System.Text.Json;
using System.Text.Json.Nodes;
using Xunit;

namespace R9.Logging.SubstrateTests
{
    public class TestUtil
    {
        [Theory]
        [InlineData(@"{""A"": 1}", "A", null, @"{}")]
        [InlineData(@"{""A"": 1}", "A", "2", @"{""A"": 2}")]
        [InlineData(@"{""A"": {""B"": 1}}", "A", "2", @"{""A"": 2}")]
        [InlineData(@"{""A"": {""B"": 1}}", "B", "2", @"{""A"": {""B"": 1}, ""B"": 2}")]
        [InlineData(@"{""A"": {""B"": 1}}", "A:B", "2", @"{""A"": {""B"": 2}}")]
        [InlineData(@"{""A"": {""B"": 1}}", "A:C", "2", @"{""A"": {""B"": 1, ""C"": 2}}")]
        public void TestUpdateJson(string json, string section, string newValue, string expected)
        {
            var actual = json.UpdateJsonSection(section, newValue);
            Assert.True(JsonNode.DeepEquals(JsonNode.Parse(expected), JsonNode.Parse(actual)));
        }
    }

    internal static class TestUtils
    {
        internal static string UpdateJsonSection(this string json, string section, string newValue = null)
        {
            var jsonObject = JsonNode.Parse(json).AsObject();
            var sections = section.Split(':');
            var subObject = jsonObject; 
            for (int i = 0; i < sections.Length - 1; i++)
            {
                subObject = subObject[sections[i]].AsObject();
            }
            if (newValue is null)
            {
                subObject.Remove(sections[^1]);
            }
            else
            {
                // Assign node if it's a json-formatted string, otherwise assign plain string
                try
                {
                    var node = JsonNode.Parse(newValue);
                    subObject[sections[^1]] = node;
                }
                catch (JsonException)
                {
                    subObject[sections[^1]] = newValue;
                }
            }
            return jsonObject.ToString();
        }
    }
}
