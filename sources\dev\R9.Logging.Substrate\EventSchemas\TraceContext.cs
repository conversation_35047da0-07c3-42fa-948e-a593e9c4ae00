﻿// <copyright file="TraceContext.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.EventSchemas
{
    using System;

    /// <summary>
    /// The trace context which is used to store the current scenario and activity id
    /// </summary>
    public static class TraceContext
    {
        /// <summary>
        /// The activity id
        /// </summary>
        public static Guid ActivityId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// The operation id
        /// </summary>
        public static Guid OperationId { get; set; } = Guid.NewGuid();
    }
}
