# Step 3 - Initialize Sampling
## [Optional] Add ECS Source for Configuration
[!include[](../../../include/AddECSSource.md)]

## Initialize RuleBasedSampler

### Interface
To integrate the RuleBasedSampler, we provide the following method:
```csharp
public static ILoggingBuilder AddRuleBasedSampler(this ILoggingBuilder builder, IConfiguration configuration)
```

>[!Note]
> 1. A service can only have one sampler. If user configure another sampler, the old sampler will be overridden.
> 2. Before calling this method, all configurations should be integrated into the IConfiguration object.

### [Recommended] Integrate with Substrate Logging Extension
It is recommended to onboard Substrate Logging Extension. [`ILoggingBuilder.AddRuleBasedSampler()`](../../../SDKs/LoggingExtensions/Reference/APIRef.md#addrulebasedsampleriloggingbuilder-iconfiguration) is already integrated with Substrate Logging Extension. 

Substrate logging extension API integrates multiple components together. And to avoid affecting services do not want use sampling, we use a flag `SubstrateLogging:UseRuleBasedSampler` to control whether to enable RuleBasedSampler. If the control flag is not found, the RuleBasedSampler is disabled.

**Section Key**: `SubstrateLogging:UseRuleBasedSampler`

`true`: Enable the configuration of RuleBasedSampler

`false`: Disable the configuration of RuleBasedSampler

```jsx
{
  "SubstrateLogging": {
    "UseRuleBasedSampler": true
  }
}
```

>[!Tip]
> It is highly recommended to put the `SubstrateLogging:UseRuleBasedSampler` in local configuration and ensure it has no other copies on ECS. Because the service may have multiple configurations for multiple categories. If the control flag is put on ECS, configurations may override each other.

# [DI](#tab/DI)

For DI projects, the configuration is loaded in Host. Use method [`IServiceCollection.AddSubstrateLogging()`](../../../SDKs/LoggingExtensions/Reference/APIRef.md#addsubstrateloggingiservicecollection-iconfiguration-action) to initialize.

```csharp
    // Load configuration in host
    var host = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration((context, config) =>
    {
        // option 1: load disk file
        config.SetBasePath(Directory.GetCurrentDirectory());
        config.AddJsonFile("appsettings.json", optional:false, reloadOnChange:true);
        // option 2: load from JSON stream
        config.AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(configDataInJSON.ToString())));
    })
    .ConfigureServices((context, services) =>
    {
        // AddRuleBasedSampler is already integrated in AddSubstrateLogging 
        services.AddSubstrateLogging(context.Configuration);
        services.AddSingleton<IHostedService, SampleService>();
    })
    .Build();
```

# [Non-DI](#tab/nonDI)
For Non-DI projects, we'll create a static LoggerFacotry and load configuration manually to configure it.

Use [`ILoggerFactory.ConfigureSubstrateLogging`](../../../SDKs/LoggingExtensions//Reference/APIRef.md#configuresubstrateloggingiloggingbuilder-iconfiguration).

[!code-csharp[](../../../include/NonDIInitLogger.cs)]

---

### Direct Integration
Directly integration is also supported. Make sure load the configuration before invoke [`ILoggingBuilder.AddRuleBasedSampler()`](../../../SDKs/LoggingExtensions/Reference/APIRef.md#addrulebasedsampleriloggingbuilder-iconfiguration).

# [DI](#tab/DI)
```csharp
IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
        Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json")
    ); 

var serviceCollection = new ServiceCollection();
serviceCollection.AddLogging(builder =>
{
    builder.AddRuleBasedSampler(configuration);
    // ... other configurations
});
```

# [Non-DI](#tab/nonDI)
```csharp
IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
        Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json")
    ); 

ILoggerFactory loggerFactory = LoggerFactory.Create(builder =>
{
    builder.AddRuleBasedSampler(configuration);
    // ... other configurations
});
```
---

Both approaches support the same features including runtime configuration updating and all sampling strategies. Choose the integration path that best fits your service's architecture and requirements.

## Summary
This document outlines the different approaches of initializing the RuleBasedSampler.

**Next Step**: [Instrument Data](./InitializeSampling.md)