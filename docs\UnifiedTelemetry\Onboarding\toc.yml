- name: Logging
  href: LoggingSolution/About.md
  items:
  - name: R9 Onboarding
    items:
    - name: Step 0 - Prerequisites
      href: LoggingSolution/GuideSteps/Prerequisites.md
    - name: Step 1 - Set Up Configuration
      href: LoggingSolution/GuideSteps/Configuration.md
    - name: Step 2 - Initialize R9 Telemetry
      href: LoggingSolution/GuideSteps/InitR9.md
    - name: Step 3 - Instrument Data
      href: LoggingSolution/GuideSteps/Instrumentation.md
    - name: Step 4 - Validate Locally
      href: LoggingSolution/GuideSteps/LocalValid.md
    - name: Step 5 - Update Monitoring Agent
      href: LoggingSolution/GuideSteps/UpdateMA.md
  - name: Migration from IFx/PassiveMon SDK
    href: IfxPassiveMonDeprecate.md
    items:
    - name: Step 0 - Prerequisites
      href: LoggingSolution/GuideSteps/MiPrerequisites.md
    - name: Step 1 - Migrate Instrumentaion
      href: LoggingSolution/GuideSteps/MigrationInstrumentaion.md
    - name: Step 2 - Check Potential Risks
      href: LoggingSolution/GuideSteps/PotentialRisks.md
    - name: Step 3 - Switch Data Seamlessly
      href: LoggingSolution/GuideSteps/MigrationSteps.md
  - name: MDS to ODL Migration
    href: LoggingSolution/OdlKustoDumper.md
    items:
    - name: Step-by-step Guide
      href: LoggingSolution/StepByStep.md
    - name: Odl Export Schema
      href: LoggingSolution/OdlSchema.md
  - name: Sampling
    href: LoggingSolution/Sampling/Sampling.md
    items:
    - name: Step 0 - Prerequisites
      href: LoggingSolution/Sampling/Prerequisites.md
    - name: Step 1 - Create Sampling Rules
      href: LoggingSolution/Sampling/CreateSamplingRules.md
      items:
        - name: Invalid Sampling Rules Example
          href: LoggingSolution/Sampling/InvalidRuleExample.md
    - name: Step 2 - Set Up Configuration
      href: LoggingSolution/Sampling/SetUpConfiguration.md
    - name: Step 3 - Initialize Sampling
      href: LoggingSolution/Sampling/InitializeSampling.md
    - name: Step 4 - Instrument Data
      href: LoggingSolution/Sampling/InstrumentData.md

- name: Metering
  href: MeteringSolution/About.md
  items:
  - name: R9 Onboarding
    items:
    - name: Step 0 - Prerequisites
      href: MeteringSolution/GuideSteps/Prerequisites.md
    - name: Step 1 - Set up Configuration
      href: MeteringSolution/GuideSteps/Configuration.md
    - name: Step 2 - Initialize R9 Telemetry
      href: MeteringSolution/GuideSteps/InitR9.md
    - name: Step 3 - Instrument Data
      href: MeteringSolution/GuideSteps/Instrumentation.md
      items:
      - name: Step 3.1 - Built-in metris
        href: MeteringSolution/GuideSteps/BuiltInMetrics.md
      - name: Step 3.2 - Event Counters
        href: MeteringSolution/GuideSteps/EventCounter.md
      - name: Step 3.3 - Instrumentation Libraries
        href: MeteringSolution/GuideSteps/InstrumentationLib.md
    - name: Step 4 - Validate Locally
      href: MeteringSolution/GuideSteps/LocalValid.md
    - name: Step 5 - Check Potential Risks
      href: MeteringSolution/GuideSteps/PotentialRisks.md
  - name: Migration from IFx/PassiveMon SDK
    href: IfxPassiveMonDeprecate.md
    items:
    - name: Step 0 - Prerequisites
      href: MeteringSolution/GuideSteps/MiPrerequisites.md
    - name: Step 1 - Migrate Instrumentaion
      href: MeteringSolution/GuideSteps/IfxMigration.md
    - name: Step 2 - Switch Data Seamlessly
      href: MeteringSolution/GuideSteps/MigrationSteps.md