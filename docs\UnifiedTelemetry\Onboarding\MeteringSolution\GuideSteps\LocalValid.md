# Step 4 - Validate Locally
## Windows
The following steps will help you validate that R9 metering is correctly configured and setup to export metrics on a Windows machine.

This is a self-contained document and you don't need a Geneva account or certificate to try this out. This is only meant to validate that the exporter is functioning correctly. The exporter uses a known ETW provider to export the metrics to ETW. We would be using a tool called MetricDog. MetricDog is a troubleshooting/diagnostics tool which decodes metric traffic capture files and dumps it in human-readable formats. Supported protocols for metric data are `ifxMetrics, statsd, influxDB, otlp` and supported output file types are `csv, tsv and json`. It also supports consuming ETW events in real-time.

It is important to note that, the OpenTelemetry .NET Geneva Exporter always works by exporting telemetry to local machine's [ETW](https://docs.microsoft.com/windows/win32/etw/about-event-tracing) on Windows, and Unix Domain Socket (UDS) on Linux. This is same whether the application runs in a local dev box, or in an Azure VM or in some special cloud environments. The exporter does not have any knowledge about where is it running. It is the agent, which reads data from ETW/UDS and uploads the data to the Geneva ingestion/backend. Because of this, if you are able to validate data using the doc, it confirms that you have successfully setup the OpenTelemetry .NET SDK and the Geneva Exporter.

### Step 1: Download MetricDog
Download MetricDog from [here](https://msblox.visualstudio.com/Azure%20Geneva%20Monitoring%20and%20Diagnostics%20Pipeline/_artifacts/feed/AzureGenevaMonitoring/NuGet/MetricDog/overview/2.2025.114.1323).
Select the latest version, and click Download button.

### Step 2: Extract MetricDog to local directory
The downloaded file will have the ".nupkg" extension. Rename the file to ".zip". And extract the zip file and copy the "MetricDog" subdirectory to a location, say `c:\MetricDog`.

### Step 3: Start MetricDog
Open Command Line and run the following commands to start MetricDog.
```shell
cd /d c:\MetricDog
MetricDog.exe -e -o "c:\MetricDog\out"
```
You should see a message like "Listening ETW and creating report..." when MetricDog successfully runs. If you see any error message, re-run the above from command line with administrative privileges.

### Step 4: Run your app
Run your app. Make sure to let the app run for at least 1 second, as metrics are exported every 1 second by default.

### Step 5: View exported data
Once the application is executed, press Ctrl + C on the MetricDog command window to stop MetricDog. "c:\MetricDog\out" should contain a file named `mdog_out.csv`. This file contains the exported metric data.

Your file output should look like this:
![mdog_out](../../../.images/Metering/UserInstructions/mdog_out.png)

Validate that MonitoringAccountName, MetricNamespace and MetricName are as expected. For each metric, there will be as many rows as the number of unique key/value pair combinations of the tags (shown in Dimensions column). As OpenTelemetry aggregates the measurements, the number of rows in the csv file is not expected to match the number of times your code explicitly emits metrics.

For non-Histogram instruments, the OpenTelemetry aggregated value of the metric will be in the MetricValue column. For Histogram instruments, the OpenTelemetry aggregated values of the metric will be in the MetricCount, Min, Max and Sum columns. Additionally, Histogram column shows the bucketed data calculated by OpenTelemetry.

### Step 6: Feel validated
Congratulations!! You have successfully setup R9 metering for metrics. The MetricDog tool listens to the metrics exported from the Geneva Exporter, and decodes the information in a human readable format. If you can see the expected metrics with this tool, it validates that your code is functioning as expected.

If you want to see metrics on Jarvis, please refer to [Set up Geneva on a TDS](https://eng.ms/docs/experiences-devices/m365-core/o365-substrate-fabric-extended-suzhou/o365-telemetry/m365-passive-monitoring/m365-passive-monitoring/geneva/tds-setup).

## Linux

You can use [dotnet-trace](https://learn.microsoft.com/dotnet/core/diagnostics/dotnet-trace) to troubleshoot R9 Geneva Metering.

1. Download and install [dotnet-trace](https://learn.microsoft.com/dotnet/core/diagnostics/dotnet-trace#install).

2. Run your application with `dotnet-trace` to collect events: `dotnet-trace collect --providers R9-GenevaMetering-Instrumentation -- Metering`. The application name in this example is Metering and we assume it is located in the current directory. The command above should produce output like this:

![dotnet-trace-start](../../../.images/Metering/UserInstructions/dotnet-trace-start.png)

3. The command ran the Metering app with all events in the `R9-GenevaMetering-Instrumentation EventSource` enabled and output the trace file `Metering_20220628_122947.nettrace`.

4. Open this file in Visual Studio, click on the Filter button and select only `R9-GenevaMetering-Instrumentation`:
 
![dotnet-trace-visual](../../../.images/Metering/UserInstructions/dotnet-trace-visual.png)

5. Click *Apply. Now you can see the events of the `R9-GenevaMetering-Instrumentation` provider. When you click on any event, you'll see more details of it in Additional Properties column.


Please continue **Next Step**: [Check Potential Risk](./PotentialRisks.md)