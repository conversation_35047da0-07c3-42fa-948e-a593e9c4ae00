﻿// <copyright file="ODLTcpTraceExporterExtensionTest.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.M365.Core.Telemetry.Exporters.ODLTCP.Trace;
using Newtonsoft.Json;
using OpenTelemetry.Trace;
using Xunit;

//#pragma warning disable CS8625 // Cannot convert null literal to non-nullable reference type.
namespace Microsoft.R9.Extensions.Telemetry.Exporters.ODLTcp.Test
{
    /// <summary>
    /// Test class for ODLTcpTraceExporterExtension.
    /// </summary>
    public class ODLTcpTraceExporterExtensionTest
    {
        /// <summary>
        /// Test invalid arguments.
        /// </summary>
        [Fact]
        public void AddODLTcpExporter_GivenInvalidArguments_ThrowsException()
        {
            Assert.Throws<ArgumentNullException>(() =>
           ((TracerProviderBuilder)null!).AddODLTcpExporter((Action<ODLTcpTraceExporterOptions>)null!, true));

            Assert.Throws<ArgumentNullException>(() =>
                    new HostBuilder().ConfigureServices(builder => builder
                            .AddOpenTelemetry().WithTracing(traceProviderBuilder =>
                            {
                                traceProviderBuilder.AddODLTcpExporter((Action<ODLTcpTraceExporterOptions>)null!, true);
                            }))
                        .Build());
            Assert.Throws<ArgumentNullException>(() =>
            {
                var options = new ODLTcpTraceExporterOptions();
                options.PrepopulatedFields = new Dictionary<string, string>
                {
                    ["TestField"] = null,
                };
            });
        }

        /// <summary>
        /// Test for valid arguments.
        /// </summary>
        [Fact]
        public void AddODLTcpExporter_GivenValidOptions_RegistersRequiredServices()
        {
            using var host = new HostBuilder()
                        .ConfigureServices(builder => builder
                            .AddOpenTelemetry().WithTracing(traceProviderBuilder =>
                            {
                                traceProviderBuilder.AddODLTcpExporter(
                                    options =>
                                    {
                                        options.PrepopulatedFields = new Dictionary<string, string>
                                        {
                                            ["TestField"] = "TestValue",
                                        };
                                        options.LogTypeMappings = new Dictionary<string, string>
                                        {
                                            ["*"] = "TestEVR",
                                        };
                                        options.RecordCountPerRequest = 1;
                                        options.EnableCleanUpArchivedFiles = true;
                                    }, true);
                            }))
                        .Build();

            var options = host.Services.GetService<IOptions<ODLTcpTraceExporterOptions>>();
            Assert.NotNull(options);
            Assert.IsAssignableFrom<IOptions<ODLTcpTraceExporterOptions>>(options);
        }

        /// <summary>
        /// Test for json configuration.
        /// </summary>
        [Fact]
        public void AddODLTcpExporter_UseSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLExporterWithParam");

                var options = new ODLTcpTraceExporterOptions();
                jsonConfigRoot.Bind("ODLExporterWithParam", options);
                config.Value = JsonConvert.SerializeObject(options, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });

                using var host = new HostBuilder()
                            .ConfigureServices(service => service
                                .AddOpenTelemetry().WithTracing(builder => builder
                                .AddODLTcpExporter(config, true)))
                            .Build();
            });
            Assert.Null(exception);
        }

        /// <summary>
        /// Test for invalid json configuration.
        /// </summary>
        [Fact]
        public void AddODLTcpExporter_UseInvalidSection_RegistersRequiredServices()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();
                var config = jsonConfigRoot.GetSection("ODLExporterEmpty");

                var options = new ODLTcpTraceExporterOptions();
                jsonConfigRoot.Bind("ODLTraceExporterEmpty", options);
                config.Value = JsonConvert.SerializeObject(options, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });

                //settings not exist
                var configEmpty = jsonConfigRoot.GetSection("ODLExporterNotExist");
                var optionsEmpty = new ODLTcpTraceExporterOptions();
                jsonConfigRoot.Bind("ODLExporterNotExist", optionsEmpty);
                configEmpty.Value = JsonConvert.SerializeObject(optionsEmpty, new JsonSerializerSettings()
                {
                    ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                });

                //settings invalid
                var configInvalid = jsonConfigRoot.GetSection("ODLTraceExporterInvalid");
                var optionInvalid = new ODLTcpTraceExporterOptions();
                jsonConfigRoot.Bind("ODLExporterInvalid", optionInvalid);
                configInvalid.Value = JsonConvert.SerializeObject(optionInvalid, new JsonSerializerSettings() { ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore });

                //whenever empty or invalid or not exist, will use the default value of the options.
                Assert.Equal(configEmpty.Value, config.Value);
                Assert.Equal(configInvalid.Value, config.Value);

                using var host = new HostBuilder()
                            .ConfigureServices(service => service
                                .AddOpenTelemetry().WithTracing(builder => builder
                                .AddODLTcpExporter(config, true)))
                            .Build();
            });
            Assert.Null(exception);
        }

        /// <summary>
        /// Test for invalid json file.
        /// </summary>
        [Fact]
        public void AddODLTcpExporter_UseInvalidFile_ThrowException()
        {
            var exception = Record.Exception(() =>
            {
                IConfigurationRoot jsonConfigRoot = new ConfigurationBuilder().AddJsonFile("appsettings2.json").Build();
            });
            Assert.NotNull(exception);
        }
    }
}
