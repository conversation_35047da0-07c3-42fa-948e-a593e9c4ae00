﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <config>
    <!--
      Where packages should go.  Visual Studio will provide a default value of $(SolutionDir) but command-line based
      package restores could end up in a different location.  Setting this value keeps Visual Studio and NuGet.exe
      in sync for packages.config based restores.
    -->
    <add key="repositoryPath" value=".\packages" />
  </config>
  <packageRestore>
    <add key="enabled" value="True" />
    <add key="automatic" value="True" />
  </packageRestore>
  <activePackageSource>
    <add key="All" value="(Aggregate source)" />
  </activePackageSource>
  <packageSources>
    <clear />
    <!--M365Infra note: Enzyme is the only supported nuget feed. For more details see aka.ms/m365coral-->
    <add key="Enzyme" value="https://o365exchange.pkgs.visualstudio.com/DefaultCollection/_packaging/Enzyme/nuget/v3/index.json" />
  </packageSources>
</configuration>