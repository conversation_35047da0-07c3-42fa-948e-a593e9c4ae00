# Step 3.3 - Instrumentation Libraries
When you develop an app, you might use third-party libraries and frameworks to accelerate your work. You might want to avoid spending additional time to manually add metrics to the third-party libraries and frameworks you use. Then instrumentaion libraries would help auto-generate metrics for them.

Available instrumentation libraries could found in [R9 Telemetry](https://eng.ms/docs/experiences-devices/r9-sdk/docs/telemetry/metering/autocollection-of-metrics/http-metering) and [OpenTelemetry](https://opentelemetry.io/docs/languages/dotnet/libraries/).