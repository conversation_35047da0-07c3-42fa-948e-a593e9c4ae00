# About Logging Solution

*<div style="text-align: right; font-size:12px">Last Modified: @@LastModified</div>*

Welcome to the **Substrate Logging Solution!** 


The solution leverages [.NET Logging]((https://learn.microsoft.com/en-us/dotnet/core/extensions/logging?tabs=command-line)) instrumentation APIs—such as ILogger and Fast Logging—alongside the [R9 Logging SDK](https://eng.ms/docs/experiences-devices/r9-sdk) to collect telemetry data.  On top of this foundation, the **Substrate R9 Logging Extension** is built to address Substrate-specific requirements without compromising flexibility. The main purpose of the Extension is to provide nearly parity functionality with PassiveMon SDK, which is widely used now, trying to provide similar use experience and reduce the impact on existing Telemetry Scenarios of Substrate services.  

## Benefits

The adopt will brings in those benefits:
- **Enabled Linux support** , enables support for Linux-based services.
- **Enable cross model supporting ability**, a unified codebase can serve multiple models (Model A, B2, COSMIC), reducing duplication and maintenance overhead. 
- **Performance improvement**, built on high-performance logging APIs like ILogger, designed for efficiency and scalability.
- **Enable new features**, Unlocks advanced capabilities such as log sampling to reduce telemetry volume, correlation with traces and metrics, and integration with instrumentation libraries for automatic log generation.
- **COGs-Saving friendly**, enables saving telemetry costs through dynamic log routing (Composite Exporter), fine-grained sampling (Rule-Based Sampler), and centralized, cost-efficient configuration via ECS and cold storage support.
- **Legacy-Free and Migration-Ready**: offers near-parity with PassiveMon —such as common dimensions enrichment and standardized base event classes—to ensure consistency, along with a unified API and streamlined tooling to simplify migration from the soon-to-be deprecated IFx SDK with minimal impact on existing Substrate telemetry scenarios.


## Main work
The implementation consists of two main parts: instrumentation and configure LoggerFactory

![](../../.images/DotNetLogging.png)

### 1. Instrumentation
Use [ILogger](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.ilogger?view=net-9.0-pp) and [Compile-time logging source generation API (aka Fast Logging)](https://learn.microsoft.com/en-us/dotnet/core/extensions/logger-message-generator) provided by [Microsoft.Extensions.Telemetry.Abstractions](https://www.nuget.org/packages/Microsoft.Extensions.Telemetry.Abstractions/). The compile-time logging solution is typically considerably faster at run time than existing logging approaches. It achieves this by eliminating boxing, temporary allocations, and copies to the maximum extent possible. Examples could be found [.NET extensions complexObjectLogging example](https://github.com/dotnet/extensions-samples/tree/0d088b48e0d4114748ad4c13103202307527f946/src/Telemetry/Logging/ComplexObjectLogging)


### 2. Configure LoggerFactory
Use [Substrate Logging Extension](../../SDKs/LoggingExtensions/About.md).  It is designed for Substrate services to configure loggerProvider with common enrichers, exporters, ECS and shared metrics built-in, allowing service teams to focus on their specific needs without worrying about fundamental details.

## Adopt the solution

We’ve provided a step-by-step guide to support your adoption process:
- If you're currently using the legacy IFx SDK, please refer to [Migration to R9 Telemetry](./../IfxPassiveMonDeprecate.md).
- If you're onboarding telemetry for the first time, start with the [Onboard R9 Telemetry](GuideSteps/Prerequisites.md).
- If you're onboarding "ODL Kusto Dumper", please refer to [MDS to ODL Migration](./OdlKustoDumper.md).
- If you're interested in "Log Sampling", please refer to [Log Sampling](./Sampling.md).