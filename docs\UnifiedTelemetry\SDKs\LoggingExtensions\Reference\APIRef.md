# Methods

## Configuration Helper

Extensions to merge configuration from both local settings and ECS.

**Namespace: Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Configuration**

### RegisterConfiguration(IHostBuilder)



#### Declaration

```c#
public static IHostBuilder RegisterConfiguration(
    this IHostBuilder hostBuilder
    )
```

#### Parameter

| Parameter     | Type           | Description                                                     |
| ------------- | -------------- | --------------------------------------------------------------- |
| `hostBuilder` | `IHostBuilder` | The host builder to register the configuration from ECS source. |

#### Return

| Return Type    | Description                                             |
| -------------- | ------------------------------------------------------- |
| `IHostBuilder` | The host builder with the registered ECS configuration. |

#### Exception

| Exception Type              | Condition                                                    |
| --------------------------- | ------------------------------------------------------------ |
| `ArgumentException`         | The sub-sections of `ECSParameters` fail to pass validation. |
| `ArgumentNullException`     | Non-nullable parameters are assigned null value.             |
| `InvalidOperationException` | The ECS configuration source is not properly initialized.    |

#### Example

```c#
var host = Host.CreateDefaultBuilder(args)
            .RegisterConfiguration(Path.Combine(Directory.GetCurrentDirectory(), "ECSDefaultConfig", "ECSDefaultConfig.json"))
```

### LoadConfiguration(string)

Merge the service side configuration from appsettings.json and the ECS side configuration.

#### Declaration

  ```c#
public static IConfiguration LoadConfiguration(
    string appsettingsPath,
)
  ```

#### Parameter

| Parameter         | Type     | Description                            |
| ----------------- | -------- | -------------------------------------- |
| `appsettingsPath` | `string` | The path to the appsettings.json file. |

#### Return

| Return Type      | Description                                                           |
| ---------------- | --------------------------------------------------------------------- |
| `IConfiguration` | The merged configuration from both `appsettings.json` and ECS source. |

#### Exception

| Exception Type              | Condition                                                    |
| --------------------------- | ------------------------------------------------------------ |
| `ArgumentException`         | The sub-sections of `ECSParameters` fail to pass validation. |
| `ArgumentNullException`     | Non-nullable parameters are assigned null value.             |
| `InvalidOperationException` | The ECS configuration source is not properly initialized.    |

#### Example

```c#
IConfiguration configuration = ConfigurationHelper.LoadConfiguration(
    Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json"));
```

## Substrate Logging Extension

Extensions to load a specific sections of an `IConfiguration` (which should be input when calling the method) and setup logging accordingly.

**Namespace: Microsoft.M365.Core.Telemetry.R9.Logging.Substrate**

### AddSubstrateLogging(IServiceCollection, IConfiguration, Action<ILoggingBuilder>)

#### Declaration
```c#
public static IServiceCollection AddSubstrateLogging(
    this IServiceCollection serviceCollection,
    IConfiguration serviceConfiguration,
    Action<ILoggingBuilder>? configure = null)
```

#### Parameter

| Parameter              | Type                       | Description                                          |
| ---------------------- | -------------------------- | ---------------------------------------------------- |
| `serviceCollection`    | `IServiceCollection`       | The service collection to add logging provider.      |
| `serviceConfiguration` | `IConfiguration`           | The configuration to use for setting up logging.     |
| `configure`            | `Action<ILoggingBuilder>?` | An optional action to configure the logging builder. |

#### Return

| Return Type          | Description                                                  |
| -------------------- | ------------------------------------------------------------ |
| `IServiceCollection` | The service collection with the added logging configuration. |

#### Example

Default setup:
```c#
serviceCollection.AddSubstrateLogging(configuration)
```

Or, config with customized actions to `ILoggingBuilder`:
```c#
serviceCollection.AddSubstrateLogging(configuration, loggingBuilder =>
{
    loggingBuilder.AddProcesser<MyProcessor>();
})
```

### ConfigureSubstrateLogging(ILoggingBuilder, IConfiguration)


#### Declaration
```c#
public static ILoggingBuilder ConfigureSubstrateLogging(
    this ILoggingBuilder loggingBuilder,
    IConfiguration serviceConfiguration)
```

#### Parameters

| Parameter              | Type              | Description                                      |
| ---------------------- | ----------------- | ------------------------------------------------ |
| `loggingBuilder`       | `ILoggingBuilder` | The logging builder to configure.                |
| `serviceConfiguration` | `IConfiguration`  | The configuration to use for setting up logging. |

#### Returns

| Return Type       | Description                                      |
| ----------------- | ------------------------------------------------ |
| `ILoggingBuilder` | The logging builder with the configured logging. |


#### Example

```c#
LoggerFactory.Create(builder =>
    {
        builder.ConfigureSubstrateLogging(configuration);
    });
```

## RuleBasedSampler Extension
Extensions to configure a rule-based sampler for logging.

**Namespace: Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler**

### AddRuleBasedSampler(ILoggingBuilder, IConfiguration)

#### Declaration
```c#
public static ILoggingBuilder AddRuleBasedSampler(
    this ILoggingBuilder loggingBuilder, 
    IConfiguration configuration)
```

#### Parameters

| Parameter        | Type              | Description                                           |
| ---------------- | ----------------- | ----------------------------------------------------- |
| `loggingBuilder` | `ILoggingBuilder` | The logging builder to configure.                     |
| `configuration`  | `IConfiguration`  | The configuration to use for setting up log sampling. |

#### Returns

| Return Type       | Description                                           |
| ----------------- | ----------------------------------------------------- |
| `ILoggingBuilder` | The logging builder with the configured log sampling. |

#### Example

```c#
LoggerFactory.Create(builder =>
    {
        builder.AddRuleBasedSampler(configuration);
    });
```