﻿// ---------------------------------------------------------------------------
// <copyright file="EventSchema.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
// ---------------------------------------------------------------------------
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.EventSchemas
{
    /// <summary>
    /// The event schema base
    /// </summary>
    public class EventSchema
    {
        /// <summary>
        /// Gets or sets the activity ID.
        /// </summary>
        public string ActivityId { get; set; }

        /// <summary>
        /// Gets or sets the operation ID.
        /// </summary>
        public string OperationId { get; set; }

        /// <summary>
        /// The Contructor
        /// </summary>
        public EventSchema()
        {
            this.ActivityId = TraceContext.ActivityId.ToString();
            this.OperationId = TraceContext.OperationId.ToString();
        }
    }
}
