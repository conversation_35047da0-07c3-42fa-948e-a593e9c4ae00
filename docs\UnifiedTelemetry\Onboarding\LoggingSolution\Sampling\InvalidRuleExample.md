# Invalid Sampling Rule Examples

This document provides examples of invalid sampling rules and explains why they are invalid. Invalid rules will be skipped at runtime.

## Type 1 Missing Required Attributes
```jsx
{
    "Constraints": [
        {
            // Invalid: Missing Field
            "Type": "String",
            "Operator": "Equals",
            "Value": "test"
        }
    ],
    "Strategy": {
        "Type": "Random",
        "SampleRate": 0.5
    }
}
```

```jsx
{
    "Constraints": [
        {
            "Field": "Message",
            "Type": "String",
            "Operator": "Equals",
            "Value": "test"
        }
    ],
    "Strategy": {
        "Type": "HashBasedRandom",
        "SampleRate": 0.5
        // Invalid: Missing hash key
    }
}
```

## Type 2 Invalid Type and Operator Combinations

```jsx
{
    "Constraints": [
        {
            "Field": "LogLevel",
            "Type": "String",  // Invalid: LogLevel should use LogLevel type
            "Operator": "StartsWith",  // Invalid: LogLevel only supports >=, <=, ==, !=
            "Value": "Warning"
        }
    ],
    "Strategy": {
        "Type": "Random",
        "SampleRate": 0.5
    }
}
```

```jsx
{
    "Constraints": [
        {
            "Field": "EventId",
            "Type": "EventId",
            "Operator": ">",  // Invalid: EventId only supports == and !=
            "Value": "100"
        }
    ],
    "Strategy": {
        "Type": "Random",
        "SampleRate": 0.5
    }
}
```

## Type 3 Invalid Type and Value Matching

```jsx
{
    "Constraints": [
        {
            "Field": "Count",
            "Type": "Long",
            "Operator": ">",
            "Value": "abc"  // Invalid: Not a valid long number
        }
    ],
    "Strategy": {
        "Type": "Random",
        "SampleRate": 0.5
    }
}
```

```jsx
{
    "Constraints": [
        {
            "Field": "LogLevel",
            "Type": "LogLevel",
            "Operator": ">=",
            "Value": "InvalidLevel"  // Invalid: Not a valid LogLevel enum value
        }
    ],
    "Strategy": {
        "Type": "Random",
        "SampleRate": 0.5
    }
}
```

## Type 4 Value Out of Range

```jsx
{
    "Constraints": [
        {
            "Field": "Message",
            "Type": "String",
            "Operator": "Equals",
            "Value": "test"
        }
    ],
    "Strategy": {
        "Type": "Random",
        "SampleRate": 1.5  // Invalid: Sample rate must be within [0,1]
    }
}
```

```jsx
{
    "Constraints": [
        {
            "Field": "EventId",
            "Type": "EventId",
            "Operator": "==",  
            "Value": "-1" // Invalid: EventId must be a non-negitive integer
        }
    ],
    "Strategy": {
        "Type": "Random",
        "SampleRate": 0.5
    }
}
```