﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net6.0</TargetFrameworks>
    <Nullable>enable</Nullable>
    <TestProjectType>UnitTest</TestProjectType>
    
    <AssemblyName>Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test</AssemblyName>
    <RootNamespace>Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Test</RootNamespace>
    <IsCodedUITest>False</IsCodedUITest>
    <LangVersion>Latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Extensions.Telemetry" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Microsoft.R9.Extensions.Logging.Exporters.Console" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\dev\R9.Logging.Substrate\R9.Logging.Substrate.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ECSDefaultConfig\ECSDefaultConfig.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
