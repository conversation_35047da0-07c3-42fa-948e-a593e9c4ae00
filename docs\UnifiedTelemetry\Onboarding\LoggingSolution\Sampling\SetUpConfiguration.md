# Step 2 - Set up Configuration

You can put the configuration of log sampling in appsettings.json file or other existing configuration files. When sampler is initialized, the configuration should be loaded.

> [!Tip]
> For customers already onboarded R9 and configured ECS, no more actions are needed in this page. 

## [Optional] Add ECS Source

[!include[](../../../include/ECSConfig.md)]

**Example**
```jsx
{
  "SubstrateLogging": {
    "RuleBasedSampler": {
      "SamplingDemoCategory": [
        {
          "Constraints": [
            {
              "Field": "Agent",
              "Type": "Enum",
              "Operator": "In",
              "Value": "Copilot,DeepSeek"
            }
          ],
          "Strategy": {
            "Type": "Random",
            "SampleRate": 0.7
          }
        },
        {
          "Constraints": [
            {
              "Field": "Severity",
              "Type": "Long",
              "Operator": "<=",
              "Value": "2"
            }
          ],
          "Strategy": {
            "Type": "HashBasedRandom",
            "SampleRate": 0.3,
            "HashKey": "UserId"
          }
        }
      ]
    }
  }
}
```

## Summary

Now we have the essential configuration. These configurations will be loaded at next step to configure log sampling.

**Next Step**: [Initialize Sampling](./InitializeSampling.md)