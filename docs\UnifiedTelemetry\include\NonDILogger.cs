/// <summary>
/// Logger name for event 1
/// </summary>
internal const string LoggerName1 = "logger 1";

/// <summary>
/// Logger name for event 2
/// </summary>
internal const string LoggerName2 = "logger 2";

/// <summary>
/// Create static logger for event 1
/// </summary>
internal static readonly Lazy<ILogger> LoggerForEvent1 = new Lazy<ILogger>(() => LoggerFactoryInstance.Value.CreateLogger(LoggerName1));

/// <summary>
/// Create static logger for event 2
/// </summary>
internal static readonly Lazy<ILogger> LoggerForEvent2 = new Lazy<ILogger>(() => LoggerFactoryInstance.Value.CreateLogger(LoggerName2));
