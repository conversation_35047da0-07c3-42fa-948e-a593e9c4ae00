# Step 1 - Create Sampling Rules

## Schema
Here is the schema for one [category](../../../Glossary.md#category). Configurations of different categories will merged under the `RuleBasedSampler` section.  

The sampling rule configuration consists of the following components:
1. Category: Specify the category the rules are set for.
2. Rules: Each rule contains constraints and a sampling strategy.
3. Constraints: Describe the scope of logs to be sampled.
4. Strategy: Determines how to sample the logs.

```jsx
{
  "SubstrateLogging": {
    "RuleBasedSampler": {
      "<Name of the target category>": [
        { // Rule
          "Constraints": [
            {
              "Field": "<Field name>",
              "Type": "<Field type>",
              "Operator": "<Operator>",
              "Value": "<Field Value>"
            },
            {
               ... //Another constraint
            }
          ],
          "Strategy": {
            "Type": "<Name of sampling strategy>",
            "SampleRate": <A double value within [0,1]>,
            "<Extra attribute required by specific strategy>": "<Value of the attribute>"
          }
        },
        { 
          ...// Another rule
        }
      ]
    }
  }
}
```
> [!Note]
>  During initialization, the sampler will check the validity of the configuration. If any part of a rule's configuration is considered **invalid**, the entire rule is considered invalid and will be **skipped**. There are validation rules for [Constraints](#validation) and Strategy([Random](#validation-1)/[HashBasedRandom](#validation-2)) sections.

## Category
Category is used to specify the category the rules are set for. 

The name of category **must match** the category name of the ILogger instance.

One way to create the ILogger instance is invoke [public Microsoft.Extensions.Logging.ILogger CreateLogger(string categoryName);](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.loggerfactory.createlogger?view=net-9.0-pp) 

Example from [Logging in C# and .NET](https://learn.microsoft.com/en-us/dotnet/core/extensions/logging?tabs=command-line)

```csharp
using Microsoft.Extensions.Logging;

using ILoggerFactory factory = LoggerFactory.Create(builder => builder.AddConsole());
ILogger logger = factory.CreateLogger("Program");
logger.LogInformation("Hello World! Logging is {Description}.", "fun");
```

## Constraints

Constraints section describe the scope of logs to be sampled.

There **must** be:
1. Field: Describe the field name.
2. Type: Specify the csharp type of the field.
3. Operator: Specify which operation will be used to compare the log and the field in constraint.
4. Value: Describe the field value.
   
### Schema
```jsx
Constraints:[
   {
      "Field":"<Field name>",
      "Type":"<Field type>",
      "Operator":"<Operator>",
      "Value":"<Field value>"
   },
   {
      ... //Another constraint
   }
]
```

>[!Note]
> 1. Whitespace in `Type` and `Operator` will be trimed. 
> 2. Whitespace in `Field` and `Value` will **not** be trimed.
> 3. Key and value of the attribute are **case-sensitive**.

### Validation
**If any requirement is violated, the rules will be marked as invalid.**

General validation rules:
   - `Field`, `Type`, `Operator`, and `Value` cannot be empty.
   - `Type` and `Operator` must be within supported ranges in the table below.
   - `Value` must meet the requirements in the table below.

Requirements for each kind of field:

| Field             | Type                                                                                                           | Operator                                                                           | Value                                      | Description                                                                    |
| ----------------- | -------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------- | ------------------------------------------ | ------------------------------------------------------------------------------ |
| Name of the field | String                                                                                                         | Equals<br> NotEquals<br> StartsWith<br> NotStartsWith<br> EndsWith<br> NotEndsWith |                                            | Do not support regex                                                           |
| Name of the field | Enum                                                                                                           | In<br> NotIn                                                                       | Values separated by commas, e.g. "1,2,3"   |                                                                                |
| Name of the field | Long                                                                                                           | ==<br> ><br> <<br> >=<br> <=<br> !=                                                | Value must be a valid long                 | Do not support integer                                                         |
| Name of the field | Double                                                                                                         | ==<br> ><br> <<br> >=<br> <=<br> !=                                                | Value must be a valid double               |                                                                                |
| LogLevel          | [LogLevel](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.loglevel?view=net-9.0-pp) | ==<br> ><br> <<br> >=<br> <=<br> !=                                                         | Value must be a valid log level            | "LogLevel >= Warning" means sample logs with level Warning/Error/Critical/None |
| EventId           | [EventId](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.eventid?view=net-9.0-pp)   | ==<br> !=                                                                          | Value must be a valid non-negative integer | Only compare the numeric part                                                  |

### "And" Matching
In constraints section, there could be several concrete constraints. All concrete constraints will be combined with "and" operation. Only when the log meets all the concrete constraints at the same time, it will be considered to match this rule.

Example
```jsx
{
   "Constraints":[
      {
         "Field":"Name",
         "Type":"String",
         "Operator":"Equals",
         "Value":"Sam"
      },
      {
         "Field":"Age",
         "Type":"Long",
         "Operator":"<",
         "Value":"20"
      }
   ]
}
```

```Csharp
using ILoggerFactory factory = LoggerFactory.Create(builder => builder.AddConsole());
ILogger logger = factory.CreateLogger("Demo");
logger.LogInformation("{Name} {Age}","Sam","18"); // Match
logger.LogInformation("{Name} {Age}","Sam","25"); // Not Match
logger.LogInformation("{Name} {Age}","David","18"); // Not Match
```

>[!Tip]
> If you have big constraints, put the most discriminating constraints at the top of the rule. This could speed up the matching.

### Empty Constraints
Constraints section is allowed to have no constraints. Empty constraints section means this rule can match all rules and directly apply the sampling strategy. In this case, if the log does not match previous rules, it will be applied to the strategy. All following rules are skipped.

It is recommended to configure a rule without constraits at the end of the rules as a default rule. 

Example
```jsx
{
  "SubstrateLogging": {
    "RuleBasedSampler": {
      "DummyCategory": [
        {
          ... // Previous rules
        },
        { // Rule with Empty constraints
          "Constraints": [],
          "Strategy": {
            "Type": "Random",
            "SampleRate": 1.0
          }
        } // All following rules will be skipped if there are
      ]
    }
  }
}
```

## Strategy
Strategy section determines how to sample the logs. All sampling strategies take the log as input and output a double value within \[0,1\). If the result is smaller than sample rate, the log will be sampled. Otherwise, the log will be discared.

Currently, we support random sampling strategy and hash-based random strategy.

### RandomStrategy

#### Schema
```jsx
{
   "Strategy":{
      "Type":"Random",
      "SampleRate": <A double value within [0,1]> // Don't add quotation marks.
   }
}
```

#### Validation
**If any requirement is violated, the rules will be marked as invalid.**

General validation rules:
- `Type` cannot be empty or null.
- `SampleRate` must be a double value within [0,1].

### HashBasedRandomStrategy

#### Schema
```jsx
{
   "Strategy":{
      "Type":"HashBasedRandom",
      "SampleRate": <A double value within [0,1]>, // Don't add quotation marks.
      "HashKey": "<Key to get the value for hashing>"
   }
}
```

#### Validation
**If any requirement is violated, the rules will be marked as invalid.**

General validation rules:
- `Type` cannot be empty or null.
- `SampleRate` must be a double value within [0,1].
- `HashKey` cannot be empty or null.

>[!Note]
> 1. If the hash key cannot be found in the log, the log will be sampled by default.
> 2. If the hash value is null, the log will be sampled by default. While empty string will be hashed and used for sampling.
> 3. All hash value will be transformed to string before hashing.
> 4. The distribution of the strategy's output depends on the hash value. So if logs have the same value to hash, the output will be the same. In this case, the sample rate will be rather 0% or 100%.


## Workflow

At runtime, the sampler gets the rules according to the category of the log. Then it tries to match the log with the rules in order. The order is determined by the occurance in the configuration. Rules appearing earlier are matched first. Once the log matches a certain rule, the sampling strategy of that rule will be applied, and there will be no further attempts to match other rules. If no rules are matched, the log will be sampled by default. To modify the default behavior, you can set a rule with [empty constraints](#empty-constraints).

Example
```jsx
{
  "SubstrateLogging": {
    "RuleBasedSampler": {
      "SamplingDemoCategory": [
        { // Rule 1
          "Constraints": [
            {
              "Field": "Agent",
              "Type": "Enum",
              "Operator": "In",
              "Value": "Copilot,DeepSeek"
            }
          ],
          "Strategy": {
            "Type": "Random",
            "SampleRate": 0.7
          }
        },
        { // Rule 2, invalid
          "Constraints": [
            {
              "Field": "Tenant",
              "Type": "String",
              "Operator": "Equal", // Typo, should be Equals
              "Value": "Torus"
            }
          ],
          "Strategy": {
            "Type": "Random",
            "SampleRate": 0.5
          }
        }
        { // Rule 3
          "Constraints": [
            {
              "Field": "Severity",
              "Type": "Long",
              "Operator": "<=",
              "Value": "2"
            }
          ],
          "Strategy": {
            "Type": "HashBasedRandom",
            "SampleRate": 0.3,
            "HashKey": "Tenant"
          }
        }
      ]
    }
  }
}
```

```csharp
...// Details of configuring the sampling, more details in following steps.
ILogger logger = LoggerFactory.CreateLogger("SamplingDemoCategory");

// Match Rule 1, use random strategy with 0.7 sample rate
logger.LogInformation("{Agent} {Tenant} {Severity}", "Copilot", "MSFT", "3"); 

// Fail to match Rule 1
// Rule 2 is invalid
// Fail to match Rule 3
// Cannot match any rules. Sample by default.
logger.LogInformation("{Agent} {Tenant} {Severity}", "Cursor", "Torus", "3"); 

// Fail to match Rule 1
// Rule 2 is invalid,
// Match Rule 3, use hash-based random strategy with 0.3 sample rate 
logger.LogInformation("{Agent} {Tenant} {Severity}", "Cursor", "Torus", "2"); 
```

## Summary
This document provides a comprehensive guide for creating sampling rules. 

**Next Step**: [Set Up Configuration](./SetUpConfiguration.md)