# Step 3.1 - Auto collection of built-in metrics
Starting from `.NET8.0`, metrics instrumentation is natively implemented. .NET library has incorporated support for [built-in](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/built-in-metrics?view=aspnetcore-9.0) metrics following the OpenTelemetry semantic conventions. When the application targets .NET8.0 and newer versions, the instrumentation library can automatically emit all built-in metrics.

This is a reference for metrics built-in for .NET, produced using the System.Diagnostics.Metrics API.
- [.NET runtime metrics](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/built-in-metrics-runtime)
- [.NET extensions metrics](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/built-in-metrics-diagnostics)
- [System.Net metrics](https://learn.microsoft.com/en-us/dotnet/core/diagnostics/built-in-metrics-system-net)
- [ASP.NET Core metrics](https://learn.microsoft.com/en-us/aspnet/core/log-mon/metrics/built-in)
- [Entity Framework Core Metrics](https://learn.microsoft.com/en-us/ef/core/logging-events-diagnostics/metrics)

Since `MeterState` option is Disabled by default. All the built-in metrics won't be collected. If you are insterested in some of above built-in metrics, please enable them in `MeterStateOverrides` explicitly. For example,

```json
"SubstrateMetering": {
  "R9Metering": {
	"MeterStateOverrides": {
	  "Microsoft.AspNetCore.Hosting": "Enabled",
      "System.Runtime": "Enabled"
	}
  }
}
```