# Step 3.2 - Auto collection of event counters
Event counters are used for lightweight, cross-platform, and near real-time performance metric collection. You can find the well-known event counters available at [Well known event counters in .NET](https://learn.microsoft.com/dotnet/core/diagnostics/available-counters).

## Event counter configuration
In your configuration file, add a section "EventCounters" to `SubstrateMetering`. For example,

```json
"SubstrateMetering": {
    "R9Metering": {
        "MeterStateOverrides": {
            "Microsoft.R9.Extensions.Metering.Collectors.EventCounters": "Enabled"
        }
    },
    "EventCounters": {
        "IncludeRecommendedDefault": true, 
        "SamplingInterval": "00:00:05",
        "Counters": {
            "System.Runtime": [ "gen-0-gc-count", "gen-0-size" ],
            "System.Net.NameResolution": [ "dns-lookups-duration", "current-dns-lookups" ]
        }
    },
    "GenevaExporter": {
        "MonitoringAccount": "O365DsApi",
        "MonitoringNamespace": "DsApiSdk"
    }
}
```
**IncludeRecommendedDefault**

`IncludeRecommendedDefault` option is used to enable the recommended default counters. Default value is false.

**SamplingInterval**

Sampling inverval for event counter data. Default value is one second

**Counters**

`Counters` option contains a list of event sources and counter names to listen for.

## Configure application to collect event counters
You can configure your application to automatically collect a set of event counters and funnel them into the R9 metric infrastructure. To do so, configure the meter provider and then call AddEventCounterCollector, like this:
```csharp
services
    .AddSubstrateMetering(config)
    .AddEventCounterCollector(config.GetSection("SubstrateMetering:EventCounters"))
```

## Recommended default counters
R9 recommends a set of event  counters from the [Well known event counters in .NET](https://learn.microsoft.com/dotnet/core/diagnostics/available-counters) to be enabled by all services. When option `IncludeRecommendedDefault` is set to true, the following counters are included in the default set:

- EventSource: **System.Runtime**

| Counter                       | Description                                                                                 |
|-------------------------------|---------------------------------------------------------------------------------------------|
| cpu-usage                     | The percent of the process's CPU usage relative to all of the system CPU resources          |
| working-set                   | The number of megabytes of physical memory mapped to the process context at a point in time |
| time-in-gc                    | The percent of time in GC since the last GC                                                 |
| alloc-rate                    | The number of bytes allocated per update interval                                           |
| exception-count               | The number of exceptions that have occurred                                                 |
| gen-2-gc-count                | The number of times GC has occurred for GEN 2 per update interval                           |
| gen-2-size                    | The number of bytes for GEN 2 GC                                                            |
| monitor-lock-contention-count | The number of times there was contention when trying to take the monitor's lock             |
| active-timer-count            | The number of Timer instances that are currently active, based on Timer.ActiveCount         |
| threadpool-queue-length       | The number of work items that are currently queued to be processed in the ThreadPool        |
| threadpool-thread-count       | The number of thread pool threads that currently exist in the ThreadPool                    |


- EventSource: **Microsoft-AspNetCore-Server-Kestrel**

| Counter                 | Description                                |
|-------------------------|--------------------------------------------|
| connection-queue-length |	The current length of the connection queue |
| request-queue-length    |	The current length of the request queue    |

