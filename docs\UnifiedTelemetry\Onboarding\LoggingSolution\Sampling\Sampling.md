# About Log Sampling
>[!Important]
> The RuleBasedSampler has not merged to the Substrate Logging Extension yet. This document is under reviewing and used to collect feedbacks at current stage. If you are interested in RuleBasedSampler, welcome to [contact us](../../../GetHelp.md). 

## Introduction of RuleBasedSampler
The RuleBasedSampler is a specialized component designed for rule-based log sampling within Substrate. Its primary objective is to meet the cogs saving requirements. Leveraging the sampling capabilities provided by the dotnet extension, the RuleBasedSampler implements the LoggingSampler interface. For each log, the sampler identifies the most appropriate rule and determines whether to sample the log based on the corresponding sampling strategy. Additionally, the sampler supports minute-level configuration auto-refresh at runtime, ensuring that it adapts dynamically to changes in the sampling rules.

> [!Note]
> In the code and docs of rule-based sampling solution, the semantic of word "sample" align with the implmentation of dotnet extension. For each log, if sampled, keep the log. If not, discard the log.

## Step-by-Step Guide
- [Prerequisites](Prerequisites.md)
- [Create Sampling Rules](CreateSamplingRules.md)
- [Set Up Configuration](SetUpConfiguration.md)
- [Initialize Sampling](InitializeSampling.md)
- [Instrument Data](InstrumentData.md)

## References
- [Invalid Sampling Rules Example](InvalidRuleExample.md)

## Sample App
Sample app is coming.