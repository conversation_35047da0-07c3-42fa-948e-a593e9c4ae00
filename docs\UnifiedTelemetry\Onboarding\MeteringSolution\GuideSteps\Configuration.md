# Step 1 - Set up Configuration
The first step to onboard R9 metering is the configuration. Configuration controls metric behavior. For example, it specifies which Geneva Account/Namespace the metric data should go to.

You may store them in a local file (like appsettings.json) or remote config service (e.g. ECS). A top-level section `SubstrateMetering` is introduced to store the metric settings. Here is an example of user config.

```json
"SubstrateMetering": {
  "R9Metering": {
	"MeterState": "Disabled",
	"MeterStateOverrides": {
	  "Microsoft.M365.Substrate.Sdk.DsApi": "Enabled"
	}
  },
  "GenevaExporter": {
	"MonitoringAccount": "O365DsApi",
	"MonitoringNamespace": "DsApiSdk"
  }
}
```
## R9Metering
R9Metering contains config of R9 MeteringOptions. It has following configuration options.

**MeterState**

`MeterState` option can be used to enable or disable metering.  By default, metering is Disabled.

Usually, an application may include some libraries inside it. Both the application and the included libraries can generate metrics. When onboarding R9 metering for an application, we only care metrics generated by the application. So, we should disable metering generated in the included libraries. Therefore, we set MeterState to **Disabled** by default.

**MeterStateOverrides** - Required

This is a required configuration option. `MeterStateOverrides` option is used to override the default meter state. This can be used to selectively enable metering for specific meter names after MeterState is Disabled. The above configuration enable `Microsoft.M365.Substrate.Sdk.DsApi` meter only. 

**MaxMetricStreams**

A metric stream is equivalent to one instrument. When you create an instrument and emit metrics using that instrument, it forms one metric stream. The default number of maximum metric streams supported is 1000, i.e., any metric streams created beyond this number are silently dropped. You can use the `MaxMetricsStreams` option to increase or decrease the maximum number of metric streams.

**MaxMetricPointsPerStream**

A metric point in a metric stream represents a unique combination of tag keys and values. Any change in the value of any of the recorded tags would result in the creation of a new metric point unless a metric point with the exact same combination of tags already exists in the metric stream. To elaborate further, consider the following example:

```csharp
var counter = meter.CreateCounter<int>("counter");
counter.Add(10, new KeyValuePair<string, object>("tag1", "value1"), new KeyValuePair<string, object>("tag2", "value2"));
counter.Add(10, new KeyValuePair<string, object>("tag1", "value1"), new KeyValuePair<string, object>("tag2", "value2"));
counter.Add(10, new KeyValuePair<string, object>("tag1", "value1"), new KeyValuePair<string, object>("tag2", "newValue"));
counter.Add(10, new KeyValuePair<string, object>("tag1", "value1"), new KeyValuePair<string, object>("tag2", "differentValue"));
counter.Add(10, new KeyValuePair<string, object>("tag1", "value1"), new KeyValuePair<string, object>("tag2", "value2"));
counter.Add(10, new KeyValuePair<string, object>("tag1", "value1"), new KeyValuePair<string, object>("tag2", "newValue"));
```

In the above example, the counter instrument is created and then the metric is recorded 6 times. There are 3 unique combinations of the tags, i.e., `tag1=value1, tag2=value2, tag1=value1, tag2=newValue, and tag1=value1, tag2=differentValue`. This will result in 3 metric points being created in the metric stream created for the counter.

The default value of metric points per metric stream is set to 2000. If the total metric points for any metric stream go beyond 2000, then the metric points will be silently dropped. Ideally, you shouldn't hit the limit, and if you are hitting the limit, it means you are emitting some high cardinality tags. You should avoid recording such high cardinality tags in metrics. If there are valid reasons and you want to capture more than 2000 metric points per stream, then you can use the `MaxMetricPointsPerStream` option to increase the limit. You can also decrease the limit if you want to ensure that all your metrics have low cardinality.

## GenevaExporter

GenevaExporter contains config of GenevaMetricExporterOptions.

**MonitoringAccount**

 Account name in Geneva where the metrics will be sent. This is a required configuration option.

**MonitoringNamespace**

Namespace in Geneva where the metrics will be sent. This is a required configuration option

**MetricExportIntervalMilliseconds**

You can configure `MetricExportIntervalMilliseconds` option to control the interval at which metrics are exported from application to the Geneva agent. Default export interval is set to 1 second. You can configure the value anywhere between 1 second to 60 seconds.
```json
"GenevaMetricExporter": {
    ...
    "MetricExportIntervalMilliseconds": 30000,
}
```

**UnixDomainSocketPath**

Unix domain socket is supported only on Linux. The default value of the option is `/var/etw/mdm_ifx.socket`, and it matches the default Unix domain socket being used by Geneva monitoring agent. If you are using a non-default Unix domain socket path (e.g., `/var/servicemetrics/mdm_ifx.socket`"), you can override it.
```json
"GenevaMetricExporter": {
    ...
    "UnixDomainSocketPath": "/var/servicemetrics/mdm_ifx.socket",
}
```
Or pass it in code:
```csharp
services
    .AddGenevaExporter(options =>
    {
        ...
        options.UnixDomainSocketPath = "/var/servicemetrics/mdm_ifx.socket";
    });
```

## Overriding the default account or namespace
You can override the default account or namespace for specific metrics by adding the `_microsoft_metrics_account` and/or `_microsoft_metrics_namespace` dimension in metrics respectively. This is useful when you want to send specific metrics to a different account or namespace than the default one. For more details, refer to our [FAQs](../../../FAQs/GenevaMetricExporter.md).


**Next Step**: [Initialize R9 Telemetry](./InitR9.md)