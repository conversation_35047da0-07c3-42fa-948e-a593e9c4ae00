﻿// <copyright file="ScenarioSchema.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>
namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.EventSchemas
{
    using Microsoft.M365.Core.Telemetry.Enrichment;

    /// <summary>
    /// Represents the schema for a scenario event.
    /// </summary>
    public class ScenarioSchema : EventSchema
    {
        /// <summary>
        /// MachineProvisioningState values for the scenario schema.
        /// </summary>
        public string MachineProvisioningState { get; set; }

        /// <summary>
        /// Gets or sets the component associated with the scenario.
        /// </summary>
        public string Component { get; set; }

        /// <summary>
        /// Gets or sets the scenario name.
        /// </summary>
        public string Scenario { get; set; }

        /// <summary>
        /// Gets or sets the sub-scenario name.
        /// </summary>
        public string SubScenario { get; set; }

        /// <summary>
        /// Gets or sets the tenant ID.
        /// </summary>
        public string TenantId { get; set; }

        /// <summary>
        /// Gets or sets the synthetic flag.
        /// </summary>
        public bool Synthetic { get; set; }

        /// <summary>
        /// Gets or sets the user PUID.
        /// </summary>
        public string UserPuid { get; set; }

        /// <summary>
        /// Gets or sets the mailbox database.
        /// </summary>
        public string MailBoxDB { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ScenarioSchema"/> class.
        /// </summary>
        public ScenarioSchema() : base()
        {
            this.MachineProvisioningState = DimensionValues.MachineProvisioningState;
            this.Component = string.Empty;
            this.Scenario = string.Empty;
            this.SubScenario = string.Empty;
            this.TenantId = string.Empty;
            this.Synthetic = false;
            this.UserPuid = string.Empty;
            this.MailBoxDB = string.Empty;
        }
    }
}
