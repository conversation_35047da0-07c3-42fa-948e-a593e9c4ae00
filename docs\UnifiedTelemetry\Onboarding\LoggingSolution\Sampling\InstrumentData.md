# Step 4 - Instrument Data
## R9 Logging
If you have already onboarded R9, it is recommened to use [R9 logging features](../GuideSteps/Instrumentation.md).

## Dotnet Logging API
[Dotnet logging API](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.ilogger?view=net-9.0-pp) is also supported.

>[!Note]
> If you are using [`ILogger.Log<TState>`](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.ilogger.log?view=net-9.0-pp#microsoft-extensions-logging-ilogger-log-1(microsoft-extensions-logging-loglevel-microsoft-extensions-logging-eventid-0-system-exception-system-func((-0-system-exception-system-string)))) method to send the log. 
> ```csharp
> public void Log<TState>(Microsoft.Extensions.Logging.LogLevel logLevel, Microsoft.Extensions.Logging.EventId eventId, TState state, Exception? exception, Func<TState,Exception?,string> formatter);
> ```
> you need to pass a instance of `IReadOnlyList<KeyValuePair<string, object?>>` or its subtype to the `State`, and put the key value pair to the instance if:
> 1. You have constraints of String, Long, Double, and Enum
> 2. You set hash-based random strategy
> Otherwise, related rules may not take effect as expected.
>
> If you are interested in the reason:
> 
> The RuleBasedSampler is built on [LoggingSampler](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.loggingsampler?view=net-9.0-pp) and implements the ShouldSample method.
>```csharp
> public abstract bool ShouldSample<TState>(in Microsoft.Extensions.Logging.Abstractions.LogEntry<TState> logEntry);
>```
> 
> Logs will be transformed to LogEntry instance before sampling. The [LogEntry](https://learn.microsoft.com/en-us/dotnet/api/microsoft.extensions.logging.abstractions.logentry-1?view=net-9.0-pp) has several properties. We leverage the Category, EventId, LogLevel, State to do sampling. Currently we support String, Long, Double, Enum, EventId, and LogLevel constraints. EventId and LogLevel are directly read from the log entry instance and the others need to be parsed from the State.

## Summary
Now we finish all required steps to enabld RuleBasedSampler in your service, congrats!