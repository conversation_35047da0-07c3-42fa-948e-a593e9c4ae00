﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFrameworks>net472;net6.0;net8.0</TargetFrameworks>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<LangVersion>10.0</LangVersion>
		<OutputType>Library</OutputType>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<PackageId>Microsoft.M365.Core.Telemetry.ODL.NrtTcpClient</PackageId>
		<PackageVersion>8.0.23</PackageVersion>
		<PackageReleaseNotes>Enable more configs for AME cert tls auth.</PackageReleaseNotes>
		<Authors>SOTELS</Authors>
		<Product>Microsoft.M365.Core.Telemetry</Product>
		<Copyright>Copyright 2021 Microsoft Corp. All rights reserved.</Copyright>
		<Description>ODL Nrt TCP client.</Description>
        <PlatformTarget>AnyCPU</PlatformTarget>
	</PropertyGroup>

	<ItemGroup>
		<None Include="..\.editorconfig" Link=".editorconfig" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Google.Protobuf" />
		<PackageReference Include="Grpc.Tools">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Configuration" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" />
		<PackageReference Include="Microsoft.Office.DataLoader.ODLNRTMessage" />
		<PackageReference Include="Newtonsoft.Json" />
		<PackageReference Include="System.Diagnostics.EventLog" />
		<ProjectReference Include="..\..\FileExporterExtension\FileExporter.csproj" />
		<Protobuf Include="$(ODLNRTMessage_ProtosPath)\ODLNRTMessage.proto" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
