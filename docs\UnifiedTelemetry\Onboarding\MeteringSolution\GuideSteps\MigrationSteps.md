# Step 2 - Switch Data Seamlessly

When we complete the sections above, migration will be continued with the following phases.

## Phase 1: Introduce the R9 telemetry and create side-by-side R9 metrics.
Turn on flight for R9 metering and keep IFx metering enabled at the same time. This is to prepare for validation between R9 metrics and the original ones and ensure the data integrity.

Start with small deploy rings and pilot services for the rollout. Update the configuration to enable both R9 and IFx metering. Validate the metrics are being ingested into Geneva.

![dual](../../../.images/Metering/UserInstructions/dualIngestion.png)

## Phase 2: Data parity check
If you want to check the data consistence with before Passive SDK, you need to conduct a parity check during the dual ingestion phase. A validation method is to duplicate the important dashboards based on new R9 metrics and compare them with existing ones.

## Phase 3: Switch to R9 metrics.
Rename R9 metrics to same with current IFx metrics and add a switch in code. Firstly, set IFx metrics on and R9 metrics off. Then with code rolls out, switch IFx metrics off and R9 metrics on at the same time for a flighting granularity like deploy rings or Dags and so on. The flighting process can be achieved by ECS without code changes.

## Phase 4: Clean up legacy PassiveMon metric.
After finishing the above three steps, legacy IFx metrics and code are safe to be deleted. You should clean all the code and usage about IFx metrics before its retirement which is September 30, 2026.